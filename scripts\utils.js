// TrueBDC CRM Automation Suite - Utility Functions

class TrueBDCUtils {
    static log(message, data = null) {
        const timestamp = new Date().toISOString();
        console.log(`[TrueBDC ${timestamp}] ${message}`, data || '');
    }

    static error(message, error = null) {
        const timestamp = new Date().toISOString();
        console.error(`[TrueBDC ${timestamp}] ERROR: ${message}`, error || '');
    }

    static async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    static createElement(tag, attributes = {}, styles = {}) {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(attr => {
            element.setAttribute(attr, attributes[attr]);
        });
        
        Object.assign(element.style, styles);
        
        return element;
    }

    static createModal(title, content, options = {}) {
        const modal = this.createElement('div', {
            id: options.id || 'truebdc-modal',
            class: 'truebdc-modal'
        }, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
        });

        const modalContent = this.createElement('div', {
            class: 'truebdc-modal-content'
        }, {
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '20px',
            maxWidth: '500px',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)'
        });

        const modalHeader = this.createElement('div', {
            class: 'truebdc-modal-header'
        }, {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px',
            paddingBottom: '10px',
            borderBottom: '1px solid #eee'
        });

        const modalTitle = this.createElement('h3', {}, {
            margin: '0',
            color: '#333',
            fontSize: '18px'
        });
        modalTitle.textContent = title;

        const closeBtn = this.createElement('button', {
            class: 'truebdc-modal-close'
        }, {
            background: 'none',
            border: 'none',
            fontSize: '24px',
            cursor: 'pointer',
            color: '#999',
            padding: '0',
            width: '30px',
            height: '30px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
        });
        closeBtn.innerHTML = '&times;';
        closeBtn.onclick = () => modal.remove();

        const modalBody = this.createElement('div', {
            class: 'truebdc-modal-body'
        });
        
        if (typeof content === 'string') {
            modalBody.innerHTML = content;
        } else {
            modalBody.appendChild(content);
        }

        modalHeader.appendChild(modalTitle);
        modalHeader.appendChild(closeBtn);
        modalContent.appendChild(modalHeader);
        modalContent.appendChild(modalBody);
        modal.appendChild(modalContent);

        // Close on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Close on Escape key
        document.addEventListener('keydown', function escapeHandler(e) {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', escapeHandler);
            }
        });

        return modal;
    }

    static showTooltip(element, text, position = 'top') {
        const tooltip = this.createElement('div', {
            class: 'truebdc-tooltip'
        }, {
            position: 'absolute',
            backgroundColor: '#333',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '12px',
            zIndex: '10001',
            whiteSpace: 'nowrap',
            opacity: '0',
            transition: 'opacity 0.3s'
        });
        
        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        let top, left;
        
        switch (position) {
            case 'top':
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 8;
                break;
        }

        tooltip.style.top = `${top + window.scrollY}px`;
        tooltip.style.left = `${left + window.scrollX}px`;
        tooltip.style.opacity = '1';

        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => tooltip.remove(), 300);
        }, 3000);

        return tooltip;
    }

    static async sendMessage(action, data = {}) {
        try {
            const response = await chrome.runtime.sendMessage({
                action,
                ...data
            });
            return response;
        } catch (error) {
            this.error('Failed to send message', error);
            return { success: false, error: error.message };
        }
    }

    static async getSettings() {
        const response = await this.sendMessage('getSettings');
        return response.success ? response.data : {};
    }

    static async getScriptState(scriptName) {
        const response = await this.sendMessage('getScriptState', { script: scriptName });
        return response.success ? response.enabled : false;
    }

    static waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }

    static waitForElements(selector, count = 1, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const checkElements = () => {
                const elements = document.querySelectorAll(selector);
                if (elements.length >= count) {
                    return Array.from(elements);
                }
                return null;
            };

            const elements = checkElements();
            if (elements) {
                resolve(elements);
                return;
            }

            const observer = new MutationObserver((mutations, obs) => {
                const elements = checkElements();
                if (elements) {
                    obs.disconnect();
                    resolve(elements);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`${count} elements matching ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }

    static isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0 && 
               rect.top >= 0 && rect.left >= 0 &&
               rect.bottom <= window.innerHeight && 
               rect.right <= window.innerWidth;
    }

    static scrollToElement(element, behavior = 'smooth') {
        if (!element) return;
        
        element.scrollIntoView({
            behavior,
            block: 'center',
            inline: 'center'
        });
    }

    static formatPhoneNumber(phone) {
        if (!phone) return '';
        
        // Remove all non-digits
        const cleaned = phone.replace(/\D/g, '');
        
        // Format as (XXX) XXX-XXXX
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        } else if (cleaned.length === 11 && cleaned[0] === '1') {
            return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
        }
        
        return phone; // Return original if can't format
    }

    static cleanPhoneNumber(phone) {
        if (!phone) return '';
        return phone.replace(/\D/g, '');
    }

    static isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    static generateId(prefix = 'truebdc') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    static copyToClipboard(text) {
        if (navigator.clipboard && window.isSecureContext) {
            return navigator.clipboard.writeText(text);
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            return new Promise((resolve, reject) => {
                if (document.execCommand('copy')) {
                    resolve();
                } else {
                    reject(new Error('Copy command failed'));
                }
                document.body.removeChild(textArea);
            });
        }
    }

    static detectCRMSystem() {
        const hostname = window.location.hostname.toLowerCase();
        
        if (hostname.includes('eleadcrm.com')) {
            return 'eleadcrm';
        } else if (hostname.includes('vinsolutions.com')) {
            return 'vinsolutions';
        } else if (hostname.includes('dealersocket.com')) {
            return 'dealersocket';
        } else if (hostname.includes('cdk.com')) {
            return 'cdk';
        }
        
        return 'unknown';
    }

    static logActivity(activity, data = {}) {
        this.sendMessage('logActivity', {
            activity,
            data: {
                ...data,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                crm: this.detectCRMSystem()
            }
        });
    }
}

// Make utils globally available
window.TrueBDCUtils = TrueBDCUtils;
