// TrueBDC CRM Automation Suite - Tab to Popup Converter

class TabToPopup {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.popup = null;
        this.defaultSize = { width: 1200, height: 800 };
        this.savedSizes = new Map();
        
        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Tab to Popup Converter');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.loadSavedSizes();
                this.setupKeyListener();
                this.isActive = true;
                
                TrueBDCUtils.log('Tab to Popup Converter activated');
                TrueBDCUtils.logActivity('tab_to_popup_activated', {
                    url: window.location.href
                });
            } else {
                TrueBDCUtils.log('Tab to Popup Converter not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Tab to Popup Converter', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/reports/i,
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/NewProspects/i,
            /vinsolutions\.com/i
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    async loadSavedSizes() {
        try {
            const result = await chrome.storage.local.get('popupSizes');
            if (result.popupSizes) {
                this.savedSizes = new Map(Object.entries(result.popupSizes));
                TrueBDCUtils.log('Loaded saved popup sizes', Object.fromEntries(this.savedSizes));
            }
        } catch (error) {
            TrueBDCUtils.error('Error loading saved popup sizes', error);
        }
    }

    async saveSizes() {
        try {
            const sizesObject = Object.fromEntries(this.savedSizes);
            await chrome.storage.local.set({ popupSizes: sizesObject });
            TrueBDCUtils.log('Saved popup sizes', sizesObject);
        } catch (error) {
            TrueBDCUtils.error('Error saving popup sizes', error);
        }
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Alt+9 (Windows) or Cmd+Option+9 (Mac)
            const isWindows = (event.ctrlKey && event.altKey && event.code === 'Digit9');
            const isMac = (event.metaKey && event.altKey && event.code === 'Digit9');
            
            if (isWindows || isMac) {
                TrueBDCUtils.log('Tab to popup shortcut detected', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    ctrlKey: event.ctrlKey,
                    altKey: event.altKey,
                    metaKey: event.metaKey,
                    code: event.code
                });

                event.preventDefault();
                event.stopPropagation();

                this.convertTabToPopup();

                TrueBDCUtils.logActivity('tab_to_popup_triggered', {
                    platform: isWindows ? 'Windows' : 'Mac',
                    timestamp: new Date().toISOString()
                });

                return false;
            }
        };

        // Add event listener
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    async convertTabToPopup() {
        try {
            // Show conversion notification
            this.showConversionNotification();

            // Get current page info
            const currentUrl = window.location.href;
            const currentTitle = document.title;
            const urlKey = this.getUrlKey(currentUrl);

            // Get saved size for this URL or use default
            const savedSize = this.savedSizes.get(urlKey) || this.defaultSize;

            // Calculate popup position (center of screen)
            const left = Math.round((screen.width - savedSize.width) / 2);
            const top = Math.round((screen.height - savedSize.height) / 2);

            // Popup features
            const features = [
                `width=${savedSize.width}`,
                `height=${savedSize.height}`,
                `left=${left}`,
                `top=${top}`,
                'menubar=no',
                'toolbar=no',
                'location=yes',
                'status=yes',
                'scrollbars=yes',
                'resizable=yes'
            ].join(',');

            TrueBDCUtils.log('Opening popup window', {
                url: currentUrl,
                size: savedSize,
                position: { left, top },
                features: features
            });

            // Open popup window
            this.popup = window.open(currentUrl, '_blank', features);

            if (this.popup) {
                // Set up popup event handlers
                this.setupPopupHandlers(this.popup, urlKey);

                // Show success message
                setTimeout(() => {
                    this.showResizeTooltip();
                }, 1000);

                // Close current tab after a short delay
                setTimeout(() => {
                    window.close();
                }, 500);

            } else {
                throw new Error('Failed to open popup window - popup blocked?');
            }

        } catch (error) {
            TrueBDCUtils.error('Error converting tab to popup', error);
            this.showErrorNotification('Failed to convert tab to popup. Please check popup blocker settings.');
        }
    }

    setupPopupHandlers(popup, urlKey) {
        // Handle popup resize to save new size
        let resizeTimeout;
        const handleResize = () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                if (popup && !popup.closed) {
                    const newSize = {
                        width: popup.outerWidth,
                        height: popup.outerHeight
                    };
                    
                    this.savedSizes.set(urlKey, newSize);
                    this.saveSizes();
                    
                    TrueBDCUtils.log('Popup resized, saved new size', {
                        urlKey: urlKey,
                        size: newSize
                    });
                }
            }, 1000);
        };

        // Set up resize listener
        popup.addEventListener('resize', handleResize);

        // Handle popup close
        popup.addEventListener('beforeunload', () => {
            TrueBDCUtils.log('Popup closing');
            TrueBDCUtils.logActivity('popup_closed', {
                urlKey: urlKey,
                finalSize: this.savedSizes.get(urlKey)
            });
        });
    }

    getUrlKey(url) {
        // Create a key for storing sizes based on URL pattern
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;
            
            // Extract meaningful part of the path
            if (pathname.includes('weblinkToday.aspx')) {
                return 'weblink';
            } else if (pathname.includes('reports')) {
                return 'reports';
            } else if (pathname.includes('NewProspects')) {
                return 'prospects';
            } else if (pathname.includes('elead_mail')) {
                return 'mail';
            } else {
                return 'default';
            }
        } catch (error) {
            return 'default';
        }
    }

    showConversionNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <div class="truebdc-spinner" style="width: 16px; height: 16px; border-width: 2px;"></div>
                <span>Converting tab to popup window...</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after conversion
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showResizeTooltip() {
        const tooltip = TrueBDCUtils.createElement('div', {
            id: 'truebdc-resize-tooltip'
        }, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '300px'
        });

        tooltip.innerHTML = `
            <div>
                <strong>💡 Tip:</strong> Resize this window to your preferred size. 
                The size will be remembered for next time!
            </div>
        `;

        document.body.appendChild(tooltip);

        // Fade in
        setTimeout(() => {
            tooltip.style.opacity = '1';
        }, 100);

        // Fade out after 5 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 300);
        }, 5000);
    }

    showErrorNotification(message) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-error-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(220, 53, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '350px'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Tab to Popup settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Close popup if open
            if (this.popup && !this.popup.closed) {
                this.popup.close();
                this.popup = null;
            }

            this.isActive = false;
            
            TrueBDCUtils.log('Tab to Popup Converter destroyed');
            TrueBDCUtils.logActivity('tab_to_popup_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Tab to Popup Converter', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            savedSizesCount: this.savedSizes.size,
            popupOpen: this.popup && !this.popup.closed
        };
    }
}

// Make class globally available
window.TabToPopup = TabToPopup;
