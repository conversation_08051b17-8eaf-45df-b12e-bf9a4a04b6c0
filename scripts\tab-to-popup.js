// TrueBDC CRM Automation Suite - Tab to Popup Converter (Simplified for iframe detection)

class TabToPopup {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.popup = null;
        this.defaultSize = { width: 1200, height: 800 };
        this.targetIframe = null;

        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Tab to Popup Converter');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.setupIframeDetection();
                this.isActive = true;

                TrueBDCUtils.log('Tab to Popup Converter activated');
                TrueBDCUtils.logActivity('tab_to_popup_activated', {
                    url: window.location.href
                });
            } else {
                TrueBDCUtils.log('Tab to Popup Converter not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Tab to Popup Converter', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        // Support both the parent page and the iframe URL
        const supportedPatterns = [
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/index\.aspx/i, // Parent page
            /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/weblink\/weblinkToday\.aspx/i, // Iframe URL
            /vinsolutions\.com/i
        ];

        return supportedPatterns.some(pattern => pattern.test(url));
    }

    setupIframeDetection() {
        TrueBDCUtils.log('🔍 Setting up iframe detection system...');

        // Look for the target iframe immediately
        TrueBDCUtils.log('📋 Initial iframe scan:');
        this.findTargetIframe();

        // Set up mutation observer to detect when iframe is added dynamically
        const observer = new MutationObserver((mutations) => {
            let hasNewNodes = false;
            mutations.forEach((mutation) => {
                if (mutation.addedNodes.length > 0) {
                    hasNewNodes = true;
                }
            });

            if (hasNewNodes) {
                TrueBDCUtils.log('🔄 DOM changed, re-scanning for iframes...');
                this.findTargetIframe();
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also check periodically in case iframe loads later
        let scanCount = 0;
        setInterval(() => {
            scanCount++;
            if (!this.targetIframe) {
                TrueBDCUtils.log(`🔄 Periodic iframe scan #${scanCount}:`);
                this.findTargetIframe();
            }
        }, 3000); // Check every 3 seconds

        TrueBDCUtils.log('✅ Iframe detection system active');
    }

    findTargetIframe() {
        // Get all iframes on the page
        const iframes = document.querySelectorAll('iframe');

        // Always log all available iframes for debugging
        TrueBDCUtils.log(`=== IFRAME DETECTION REPORT ===`);
        TrueBDCUtils.log(`Total iframes found: ${iframes.length}`);

        if (iframes.length > 0) {
            TrueBDCUtils.log('All available iframes:');
            Array.from(iframes).forEach((iframe, index) => {
                const iframeInfo = {
                    index: index + 1,
                    src: iframe.src || '(no src)',
                    id: iframe.id || '(no id)',
                    className: iframe.className || '(no class)',
                    name: iframe.name || '(no name)',
                    width: iframe.width || iframe.style.width || '(no width)',
                    height: iframe.height || iframe.style.height || '(no height)',
                    display: iframe.style.display || 'default',
                    visibility: iframe.style.visibility || 'default'
                };

                console.log(`  ${index + 1}.`, iframeInfo);

                // Check if this iframe contains our target URL
                if (iframe.src && iframe.src.includes('weblink/weblinkToday.aspx')) {
                    this.targetIframe = iframe;
                    TrueBDCUtils.log(`🎯 TARGET IFRAME FOUND at index ${index + 1}!`, iframeInfo);
                }
            });
        } else {
            TrueBDCUtils.log('❌ No iframes found on this page');
        }

        // Summary
        if (this.targetIframe) {
            TrueBDCUtils.log(`✅ Target iframe detected: ${this.targetIframe.src}`);
        } else {
            TrueBDCUtils.log(`⚠️ Target iframe NOT found. Looking for URLs containing: 'weblink/weblinkToday.aspx'`);
        }

        TrueBDCUtils.log(`=== END IFRAME REPORT ===`);
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Check for Ctrl+Alt+9 (Windows only as specified)
            if (event.ctrlKey && event.altKey && event.code === 'Digit9') {
                TrueBDCUtils.log('Tab to popup shortcut detected (Ctrl+Alt+9)', {
                    ctrlKey: event.ctrlKey,
                    altKey: event.altKey,
                    code: event.code,
                    targetIframe: !!this.targetIframe
                });

                event.preventDefault();
                event.stopPropagation();

                this.convertToPopup();

                TrueBDCUtils.logActivity('tab_to_popup_triggered', {
                    timestamp: new Date().toISOString(),
                    hasTargetIframe: !!this.targetIframe
                });

                return false;
            }
        };

        // Add event listener to capture the shortcut
        window.addEventListener('keydown', this.keydownHandler, true);
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    convertToPopup() {
        try {
            let targetUrl;

            // Determine what URL to open in popup
            if (this.targetIframe && this.targetIframe.src) {
                // If we found the target iframe, use its URL
                targetUrl = this.targetIframe.src;
                TrueBDCUtils.log('Using iframe URL for popup', { url: targetUrl });
            } else {
                // Fallback: construct the URL manually
                targetUrl = 'https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx?bDashBoard=true&tab=liHotOpportunities';
                TrueBDCUtils.log('Using fallback URL for popup', { url: targetUrl });
            }

            // Show conversion notification
            this.showConversionNotification();

            // Calculate popup position (center of screen)
            const left = Math.round((screen.width - this.defaultSize.width) / 2);
            const top = Math.round((screen.height - this.defaultSize.height) / 2);

            // Popup features
            const features = [
                `width=${this.defaultSize.width}`,
                `height=${this.defaultSize.height}`,
                `left=${left}`,
                `top=${top}`,
                'menubar=no',
                'toolbar=no',
                'location=yes',
                'status=yes',
                'scrollbars=yes',
                'resizable=yes'
            ].join(',');

            TrueBDCUtils.log('Opening popup window', {
                url: targetUrl,
                size: this.defaultSize,
                position: { left, top },
                features: features
            });

            // Open popup window
            this.popup = window.open(targetUrl, '_blank', features);

            if (this.popup) {
                // Show success message
                setTimeout(() => {
                    this.showSuccessNotification();
                }, 1000);

                TrueBDCUtils.logActivity('popup_opened_successfully', {
                    url: targetUrl,
                    size: this.defaultSize
                });

            } else {
                throw new Error('Failed to open popup window - popup blocked?');
            }

        } catch (error) {
            TrueBDCUtils.error('Error converting to popup', error);
            this.showErrorNotification('Failed to convert to popup. Please check popup blocker settings.');
        }
    }



    showConversionNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-popup-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #007bff, #0056b3)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>🚀</span>
                <span>Opening popup window...</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after conversion
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    showSuccessNotification() {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-success-notification'
        }, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #28a745, #20c997)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(40, 167, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        notification.innerHTML = `
            <div>
                <strong>✅ Success:</strong> Popup window opened successfully!
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 100);

        // Fade out after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    showErrorNotification(message) {
        const notification = TrueBDCUtils.createElement('div', {
            id: 'truebdc-error-notification'
        }, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: 'linear-gradient(135deg, #dc3545, #c82333)',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            fontSize: '14px',
            fontWeight: '500',
            zIndex: '999999',
            boxShadow: '0 4px 12px rgba(220, 53, 69, 0.3)',
            opacity: '0',
            transition: 'opacity 0.3s ease',
            maxWidth: '350px'
        });

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span>⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Fade in
        setTimeout(() => {
            notification.style.opacity = '1';
        }, 10);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        TrueBDCUtils.log('Tab to Popup settings updated', newSettings);
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-detect iframe on page change
                this.targetIframe = null;
                this.findTargetIframe();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                window.removeEventListener('keydown', this.keydownHandler, true);
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Close popup if open
            if (this.popup && !this.popup.closed) {
                this.popup.close();
                this.popup = null;
            }

            this.isActive = false;
            this.targetIframe = null;

            TrueBDCUtils.log('Tab to Popup Converter destroyed');
            TrueBDCUtils.logActivity('tab_to_popup_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Tab to Popup Converter', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com|vinsolutions\.com/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            targetIframeFound: !!this.targetIframe,
            targetIframeSrc: this.targetIframe ? this.targetIframe.src : null,
            popupOpen: this.popup && !this.popup.closed
        };
    }
}

// Make class globally available
window.TabToPopup = TabToPopup;
