// TrueBDC CRM Automation Suite - Simple Auto Refresh with Timer and Modal
// EXACT COPY of working Tampermonkey script adapted for Chrome extension

class AutoRefresh {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;

        this.init();
    }

    init() {
        console.log('[TrueBDC AutoRefresh] Initializing...');
        console.log('[TrueBDC AutoRefresh] Current URL:', window.location.href);

        // Check if we're on a supported page
        if (this.isSupportedPage()) {
            console.log('[TrueBDC AutoRefresh] Supported page detected, starting script');
            this.startScript();
            this.isActive = true;
        } else {
            console.log('[TrueBDC AutoRefresh] Not a supported page');
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        console.log('[TrueBDC AutoRefresh] Full URL:', url);
        console.log('[TrueBDC AutoRefresh] Hostname:', window.location.hostname);
        console.log('[TrueBDC AutoRefresh] Pathname:', window.location.pathname);

        // EXACT URL matching as specified: https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*
        const isSupported = url.includes('eleadcrm.com') &&
                           url.includes('/elead_track/weblink/weblinkToday.aspx');
        console.log('[TrueBDC AutoRefresh] URL check - Supported:', isSupported);
        console.log('[TrueBDC AutoRefresh] Looking for: /elead_track/weblink/weblinkToday.aspx');
        return isSupported;
    }

    startScript() {
        console.log('[TrueBDC AutoRefresh] Starting the exact working script...');

        // EXACT COPY of the working Tampermonkey script
        (function() {
            'use strict';

            /** @type {number} */
            let intervalId = null;
            /** @type {boolean} */
            let isPaused = false;
            /** @type {number} */
            let countdown = 0;
            /** @type {number} */
            let refreshTime = Number(localStorage.getItem('autoRefreshTime')) || 5;

            console.log('[TrueBDC AutoRefresh] Script variables initialized:', { intervalId, isPaused, countdown, refreshTime });

            /**
             * Create or update the timer display in the corner.
             */
            function createTimerDisplay() {
                console.log('[TrueBDC AutoRefresh] Creating timer display...');
                let timer = document.getElementById('autoRefreshTimerDisplay');
                if (!timer) {
                    console.log('[TrueBDC AutoRefresh] Timer element not found, creating new one');
                    timer = document.createElement('div');
                    timer.id = 'autoRefreshTimerDisplay';
                    timer.style.position = 'fixed';
                    timer.style.bottom = '20px';
                    timer.style.left = '20px';
                    timer.style.background = '#fff';
                    timer.style.color = '#222';
                    timer.style.padding = '10px 18px';
                    timer.style.borderRadius = '8px';
                    timer.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
                    timer.style.fontFamily = 'sans-serif';
                    timer.style.fontSize = '16px';
                    timer.style.cursor = 'pointer';
                    timer.style.zIndex = 10000;
                    timer.title = 'Click to set refresh interval';
                    timer.addEventListener('click', showModal);
                    document.body.appendChild(timer);
                    console.log('[TrueBDC AutoRefresh] Timer element created and added to DOM');
                } else {
                    console.log('[TrueBDC AutoRefresh] Timer element found, updating content');
                }
                timer.textContent = isPaused ? `⏸️ Paused` : `🔄 Refresh in ${countdown}s`;
                timer.style.opacity = isPaused ? '0.5' : '1';
                console.log('[TrueBDC AutoRefresh] Timer display updated:', timer.textContent);
            }

            /**
             * Show a modal to set the refresh interval.
             */
            function showModal() {
                console.log('[TrueBDC AutoRefresh] showModal called');
                if (document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Modal already exists, returning');
                    return;
                }
                isPaused = true;
                updateDisplay();

                const timer = document.getElementById('autoRefreshTimerDisplay');
                const timerRect = timer.getBoundingClientRect();
                console.log('[TrueBDC AutoRefresh] Timer position:', timerRect);

                const modal = document.createElement('div');
                modal.id = 'refreshTimeModal';
                modal.style.position = 'fixed';
                modal.style.left = timerRect.left + 'px';
                modal.style.bottom = (window.innerHeight - timerRect.top) + 'px';
                modal.style.background = '#fff';
                modal.style.padding = '5px 7.5px'; // Modified: 50% reduced padding
                modal.style.borderRadius = '10px';
                modal.style.boxShadow = '0 4px 16px rgba(0,0,0,0.2)';
                modal.style.zIndex = 10001;
                modal.style.fontFamily = 'sans-serif';
                modal.style.fontSize = '12px'; // Modified: reduced font size

                modal.innerHTML = `
                    <label for="refreshTimeSelect" style="font-size:12px;">Refresh every:</label>
                    <select id="refreshTimeSelect" style="margin:0 5px; font-size:12px;">
                        <option value="5">5s</option>
                        <option value="6">6s</option>
                        <option value="7">7s</option>
                        <option value="8">8s</option>
                        <option value="9">9s</option>
                        <option value="10">10s</option>
                    </select>
                    <button id="okBtn" style="margin-left:5px; font-size:12px;">OK</button>
                `;

                document.body.appendChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal created and added to DOM');

                document.getElementById('refreshTimeSelect').value = refreshTime;
                document.getElementById('okBtn').onclick = function() {
                    console.log('[TrueBDC AutoRefresh] OK button clicked');
                    refreshTime = Number(document.getElementById('refreshTimeSelect').value);
                    localStorage.setItem('autoRefreshTime', refreshTime);
                    console.log('[TrueBDC AutoRefresh] New refresh time set:', refreshTime);
                    closeModal();
                    restart();
                };

                modal.addEventListener('click', e => e.stopPropagation());
                setTimeout(() => {
                    document.addEventListener('click', closeModal, { once: true });
                }, 0);
            }

            /**
             * Close the modal if open.
             */
            function closeModal() {
                console.log('[TrueBDC AutoRefresh] closeModal called');
                const modal = document.getElementById('refreshTimeModal');
                if (modal) {
                    modal.remove();
                    console.log('[TrueBDC AutoRefresh] Modal removed');
                }
                isPaused = false;
                updateDisplay();
            }

            /**
             * Update the timer display.
             */
            function updateDisplay() {
                createTimerDisplay();
            }

            /**
             * Start the auto-refresh timer.
             */
            function start() {
                console.log('[TrueBDC AutoRefresh] start() called');
                if (intervalId) {
                    clearInterval(intervalId);
                    console.log('[TrueBDC AutoRefresh] Cleared existing interval');
                }
                countdown = refreshTime;
                updateDisplay();
                intervalId = setInterval(() => {
                    if (!isPaused) {
                        countdown--;
                        updateDisplay();
                        console.log('[TrueBDC AutoRefresh] Countdown:', countdown);
                        if (countdown <= 0) {
                            console.log('[TrueBDC AutoRefresh] Countdown reached 0, refreshing page...');
                            document.getElementById('autoRefreshTimerDisplay').textContent = '🔄 Refreshing...';
                            setTimeout(() => window.location.reload(), 700);
                            clearInterval(intervalId);
                        }
                    }
                }, 1000);
                console.log('[TrueBDC AutoRefresh] Timer started with interval:', intervalId);
            }

            /**
             * Restart the timer.
             */
            function restart() {
                console.log('[TrueBDC AutoRefresh] restart() called');
                isPaused = false;
                start();
            }

            // Initialize
            console.log('[TrueBDC AutoRefresh] Initializing timer...');
            start();

            // Keyboard shortcut: Space to pause/resume
            document.addEventListener('keydown', e => {
                if (e.code === 'Space' && !document.getElementById('refreshTimeModal')) {
                    console.log('[TrueBDC AutoRefresh] Spacebar pressed, toggling pause');
                    isPaused = !isPaused;
                    updateDisplay();
                }
            });

            console.log('[TrueBDC AutoRefresh] Script initialization complete');

        })();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        console.log('[TrueBDC AutoRefresh] Settings updated:', newSettings);
    }

    onPageChange() {
        console.log('[TrueBDC AutoRefresh] Page change detected');
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        console.log('[TrueBDC AutoRefresh] Destroying script...');
        try {
            // Remove UI elements
            const timer = document.getElementById('autoRefreshTimerDisplay');
            if (timer && timer.parentNode) {
                timer.parentNode.removeChild(timer);
                console.log('[TrueBDC AutoRefresh] Timer element removed');
            }

            const modal = document.getElementById('refreshTimeModal');
            if (modal && modal.parentNode) {
                modal.parentNode.removeChild(modal);
                console.log('[TrueBDC AutoRefresh] Modal element removed');
            }

            this.isActive = false;
            console.log('[TrueBDC AutoRefresh] Script destroyed');
        } catch (error) {
            console.error('[TrueBDC AutoRefresh] Error destroying script:', error);
        }
    }

    static isAvailable() {
        const url = window.location.href;
        return url.includes('eleadcrm.com') && url.includes('/elead_track/weblink/weblinkToday.aspx');
    }

    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasTimer: !!document.getElementById('autoRefreshTimerDisplay')
        };
    }
}

// Make class globally available
window.AutoRefresh = AutoRefresh;
