// TrueBDC CRM Automation Suite - Add Calling Text and Shortcut to Change Time (Condensed Modal, Tooltip Only With Modal)

class CallingText {
    constructor(settings = {}) {
        this.settings = settings;
        this.isActive = false;
        this.keydownHandler = null;
        this.repName = settings.agentName || '';

        // Constants matching the original script
        this.TEXTAREA_ID = 'textComments';
        this.SAVE_BUTTON_ID = 'buttonSave';
        this.REP_NAME_KEY = 'repName';
        this.DEFAULT_REP_NAME = 'Rep';

        this.init();
    }

    init() {
        try {
            TrueBDCUtils.log('Initializing Add Calling Text and Time Shortcut');

            // Check if we're on a supported page
            if (this.isSupportedPage()) {
                this.setupKeyListener();
                this.initializeScript();
                this.isActive = true;

                TrueBDCUtils.log('Add Calling Text and Time Shortcut activated');
                TrueBDCUtils.logActivity('calling_text_activated', {
                    url: window.location.href,
                    repName: this.repName
                });
            } else {
                TrueBDCUtils.log('Add Calling Text and Time Shortcut not activated - unsupported page');
            }
        } catch (error) {
            TrueBDCUtils.error('Failed to initialize Add Calling Text and Time Shortcut', error);
        }
    }

    isSupportedPage() {
        const url = window.location.href;
        // Exact URL pattern from the original script
        const supportedPattern = /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/eleadToday\/CompletePhoneContact\.aspx/i;

        return supportedPattern.test(url);
    }

    setupKeyListener() {
        this.keydownHandler = (event) => {
            // Ctrl+M or Cmd+M: Increment time by 1 minute
            if ((event.ctrlKey && event.key === 'm') || (event.metaKey && event.key === 'm')) {
                event.preventDefault();
                this.incrementDateTime();
                TrueBDCUtils.log('DateTime increment shortcut (Ctrl/Cmd+M) triggered');
                return false;
            }

            // Ctrl+K or Cmd+K: Increment time and click complete
            if ((event.ctrlKey && event.key === 'k') || (event.metaKey && event.key === 'k')) {
                event.preventDefault();
                this.incrementDateTime();
                this.clickCompleteButton();
                TrueBDCUtils.log('DateTime increment + Complete shortcut (Ctrl/Cmd+K) triggered');
                return false;
            }

            // Rep name modal: Ctrl+Alt+9, Cmd+Alt+9, Ctrl+Shift+R, Cmd+Shift+R
            if ((event.ctrlKey && event.altKey && event.key === '9') ||
                (event.metaKey && event.altKey && event.key === '9') ||
                (event.ctrlKey && event.shiftKey && event.key.toLowerCase() === 'r') ||
                (event.metaKey && event.shiftKey && event.key.toLowerCase() === 'r')) {
                event.preventDefault();
                this.showModal();
                TrueBDCUtils.log('Rep name modal shortcut triggered');
                return false;
            }
        };

        // Add event listener
        document.addEventListener('keydown', this.keydownHandler, true);
    }

    initializeScript() {
        // Initialize the script like the original
        this.updateTextarea();
        this.maybeShowInput();
    }

    // Tooltip logic
    showShortcutTooltip() {
        let helpDiv = document.getElementById('shortcutHelpTooltip');
        if (helpDiv) return; // Already shown

        helpDiv = TrueBDCUtils.createElement('div', {
            id: 'shortcutHelpTooltip'
        }, {
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: 'rgba(0,0,0,0.8)',
            color: '#fff',
            padding: '7px 12px',
            borderRadius: '5px',
            fontSize: '11px',
            fontFamily: 'monospace',
            zIndex: '10001',
            maxWidth: '220px',
            lineHeight: '1.4'
        });

        helpDiv.innerHTML = `
            <strong>Shortcuts:</strong><br>
            <u>Windows</u>:<br>
            Ctrl+M: +1 minute<br>
            Ctrl+Alt+9: Rep name<br>
            <u>Mac</u>:<br>
            Cmd+M: +1 minute<br>
            Cmd+Alt+9: Rep name
        `;

        document.body.appendChild(helpDiv);
    }

    hideShortcutTooltip() {
        const helpDiv = document.getElementById('shortcutHelpTooltip');
        if (helpDiv) helpDiv.remove();
    }

    // Rep name management module
    getRepName() {
        return localStorage.getItem(this.REP_NAME_KEY) || this.DEFAULT_REP_NAME;
    }

    setRepName(name) {
        if (name && name.trim()) {
            localStorage.setItem(this.REP_NAME_KEY, name.trim());
            this.updateTextarea();
            this.hideModal();
        }
    }

    clearRepName() {
        localStorage.removeItem(this.REP_NAME_KEY);
        this.updateTextarea();
    }

    // Function to update the textarea with rep name and "Calling" text
    updateTextarea() {
        const textareaElement = document.getElementById(this.TEXTAREA_ID);
        if (textareaElement) {
            textareaElement.value = `${this.getRepName()} -- Calling`;
            textareaElement.focus();
        }
    }

    // Get date input element using the new selector
    getDateInputElement() {
        return document.querySelector('input[name="date-time-picker-time"]');
    }

    // Function to add 1 minute to the date/time input (improved version)
    incrementDateTime() {
        const dateInputElement = this.getDateInputElement();
        if (!dateInputElement) return;

        const currentValue = dateInputElement.value.trim();
        const timeMatch = currentValue.match(/^(\d{1,2}):(\d{2})\s*(am|pm)$/i);
        if (!timeMatch) return;

        let [, hours, minutes, ampm] = timeMatch;
        hours = parseInt(hours, 10);
        minutes = parseInt(minutes, 10);

        minutes += 1;
        if (minutes >= 60) {
            minutes = 0;
            hours += 1;
            if (hours > 12) hours = 1;
            if (hours === 12 && ampm.toLowerCase() === 'am') ampm = 'pm';
            else if (hours === 13) {
                hours = 1;
                ampm = ampm.toLowerCase() === 'am' ? 'pm' : 'am';
            }
        }

        const formattedTime = `${hours}:${minutes.toString().padStart(2, '0')} ${ampm.toLowerCase()}`;
        dateInputElement.value = formattedTime;

        // Trigger events
        dateInputElement.dispatchEvent(new Event('input', { bubbles: true }));
        dateInputElement.dispatchEvent(new Event('change', { bubbles: true }));

        TrueBDCUtils.log('DateTime incremented by 1 minute', {
            newValue: formattedTime
        });
    }

    // Function to click the "Complete" button
    clickCompleteButton() {
        const saveButton = document.getElementById(this.SAVE_BUTTON_ID);
        if (saveButton) {
            saveButton.click();
            TrueBDCUtils.log('Complete button clicked');
        }
    }

    // Compact modal
    createModal() {
        const existingModal = document.getElementById('repNameModal');
        if (existingModal) existingModal.remove();

        this.showShortcutTooltip();

        const modalOverlay = TrueBDCUtils.createElement('div', {
            id: 'repNameModal'
        }, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100vw',
            height: '100vh',
            background: 'rgba(0,0,0,0.25)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '10000'
        });

        const modalContent = TrueBDCUtils.createElement('div', {}, {
            background: '#fff',
            padding: '16px 18px 12px 18px',
            borderRadius: '7px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.18)',
            minWidth: '220px',
            maxWidth: '320px',
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            textAlign: 'center'
        });

        const title = TrueBDCUtils.createElement('div', {}, {
            fontWeight: 'bold',
            marginBottom: '8px',
            fontSize: '15px',
            color: '#333'
        });
        title.textContent = 'Rep Name';

        const repNameInput = TrueBDCUtils.createElement('input', {
            type: 'text',
            placeholder: 'Enter your name'
        }, {
            width: '90%',
            padding: '6px 8px',
            marginBottom: '10px',
            border: '1px solid #bbb',
            borderRadius: '4px',
            fontSize: '14px'
        });
        repNameInput.value = this.getRepName() === this.DEFAULT_REP_NAME ? '' : this.getRepName();

        const buttonContainer = TrueBDCUtils.createElement('div', {}, {
            display: 'flex',
            gap: '6px',
            justifyContent: 'center'
        });

        const saveBtn = TrueBDCUtils.createElement('button', {}, {
            background: '#007cba',
            color: '#fff',
            border: 'none',
            padding: '6px 14px',
            borderRadius: '4px',
            fontSize: '13px',
            cursor: 'pointer'
        });
        saveBtn.textContent = 'Save';

        const clearBtn = TrueBDCUtils.createElement('button', {}, {
            background: '#dc3545',
            color: '#fff',
            border: 'none',
            padding: '6px 14px',
            borderRadius: '4px',
            fontSize: '13px',
            cursor: 'pointer'
        });
        clearBtn.textContent = 'Clear';

        const cancelBtn = TrueBDCUtils.createElement('button', {}, {
            background: '#6c757d',
            color: '#fff',
            border: 'none',
            padding: '6px 14px',
            borderRadius: '4px',
            fontSize: '13px',
            cursor: 'pointer'
        });
        cancelBtn.textContent = 'Cancel';

        // Event handlers
        saveBtn.onclick = () => this.setRepName(repNameInput.value);
        clearBtn.onclick = () => {
            this.clearRepName();
            this.hideModal();
        };
        cancelBtn.onclick = () => this.hideModal();

        repNameInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.setRepName(repNameInput.value);
            }
            if (e.key === 'Escape') {
                e.preventDefault();
                this.hideModal();
            }
        });

        modalOverlay.addEventListener('click', (e) => {
            if (e.target === modalOverlay) this.hideModal();
        });

        // Assemble modal
        buttonContainer.appendChild(saveBtn);
        buttonContainer.appendChild(clearBtn);
        buttonContainer.appendChild(cancelBtn);

        modalContent.appendChild(title);
        modalContent.appendChild(repNameInput);
        modalContent.appendChild(buttonContainer);
        modalOverlay.appendChild(modalContent);
        document.body.appendChild(modalOverlay);

        setTimeout(() => {
            repNameInput.focus();
            repNameInput.select();
        }, 100);
    }

    hideModal() {
        const modal = document.getElementById('repNameModal');
        if (modal) modal.remove();
        this.hideShortcutTooltip();
    }

    showModal() {
        this.createModal();
    }

    // Only show input if name is default
    maybeShowInput() {
        if (this.getRepName() === this.DEFAULT_REP_NAME) {
            this.showModal();
        }
    }



    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };

        if (newSettings.agentName && newSettings.agentName.trim()) {
            this.repName = newSettings.agentName.trim();
            TrueBDCUtils.log('Rep name updated from settings', { repName: this.repName });
        }
    }

    onPageChange() {
        // Handle page changes in SPAs
        if (this.isSupportedPage()) {
            if (!this.isActive) {
                this.init();
            } else {
                // Re-initialize on page change
                this.initializeScript();
            }
        } else {
            if (this.isActive) {
                this.destroy();
            }
        }
    }

    destroy() {
        try {
            // Remove event listeners
            if (this.keydownHandler) {
                document.removeEventListener('keydown', this.keydownHandler, true);
                this.keydownHandler = null;
            }

            // Remove modal if present
            this.hideModal();

            // Remove tooltip if present
            this.hideShortcutTooltip();

            this.isActive = false;

            TrueBDCUtils.log('Add Calling Text and Time Shortcut destroyed');
            TrueBDCUtils.logActivity('calling_text_destroyed');
        } catch (error) {
            TrueBDCUtils.error('Error destroying Add Calling Text and Time Shortcut', error);
        }
    }

    // Static method to check if script should be available
    static isAvailable() {
        const url = window.location.href;
        return /eleadcrm\.com\/evo2\/fresh\/elead-v45\/elead_track\/eleadToday\/CompletePhoneContact\.aspx/i.test(url);
    }

    // Get current status
    getStatus() {
        return {
            isActive: this.isActive,
            isSupported: this.isSupportedPage(),
            hasKeyListener: !!this.keydownHandler,
            repName: this.getRepName(),
            shortcuts: {
                incrementTime: 'Ctrl+M (Windows) / Cmd+M (Mac)',
                incrementAndComplete: 'Ctrl+K (Windows) / Cmd+K (Mac)',
                showRepModal: 'Ctrl+Alt+9, Ctrl+Shift+R (Windows) / Cmd+Alt+9, Cmd+Shift+R (Mac)'
            }
        };
    }
}

// Make class globally available
window.CallingText = CallingText;
