/* TrueBDC CRM Automation Suite - Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: transparent;
    color: #333;
    width: 400px;
    min-height: 600px;
    margin: 0;
    padding: 0;
}

.container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.logo img {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.version {
    font-size: 12px;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
}

/* Navigation Tabs */
.tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.2s ease;
    position: relative;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    color: #007bff;
    background: white;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: #007bff;
}

/* Content */
.content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Script Groups */
.script-group {
    margin-bottom: 24px;
}

.script-group h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #e9ecef;
}

.script-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.script-item:last-child {
    border-bottom: none;
}

.script-info {
    flex: 1;
    margin-right: 16px;
}

.script-name {
    display: block;
    font-weight: 500;
    color: #212529;
    margin-bottom: 4px;
}

.script-desc {
    display: block;
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background-color: #28a745;
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Settings */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #e9ecef;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.2s ease;
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-right: 8px;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.settings-actions {
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

/* Profiles */
.profile-section h3 {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
    padding-bottom: 6px;
    border-bottom: 2px solid #e9ecef;
}

.profile-actions {
    margin-bottom: 16px;
}

.profile-list {
    max-height: 300px;
    overflow-y: auto;
}

.profile-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 8px;
    background: #f8f9fa;
}

.profile-info {
    flex: 1;
}

.profile-name {
    font-weight: 500;
    color: #212529;
}

.profile-desc {
    font-size: 12px;
    color: #6c757d;
}

/* Footer */
.footer {
    background: #f8f9fa;
    padding: 12px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
}

.links {
    display: flex;
    gap: 12px;
}

.links a {
    font-size: 12px;
    color: #007bff;
    text-decoration: none;
}

.links a:hover {
    text-decoration: underline;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
