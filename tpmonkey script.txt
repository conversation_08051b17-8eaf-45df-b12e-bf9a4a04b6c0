{"created_by": "Tam<PERSON>mon<PERSON>", "version": "1", "scripts": [{"name": "Dynamic Tab Title Changer", "options": {"check_for_updates": true, "user_modified": 1714413347232, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*", "https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/reports/Desklog/Desklog.aspx*", "https://www.eleadcrm.com/rt/MessengerClient/Home/Index*", "https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/elead_mail/Mailbox.aspx?Type=M"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434156, "data": {}}, "enabled": true, "position": 1, "uuid": "9854e6f0-daa9-453a-ba83-489402d1d53f", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBEeW5hbWljIFRhYiBUaXRsZSBDaGFuZ2VyCi8vIEBuYW1lc3BhY2UgICAgaHR0cDovL3RhbXBlcm1vbmtleS5uZXQvCi8vIEB2ZXJzaW9uICAgICAgMS4wCi8vIEBkZXNjcmlwdGlvbiAgRHluYW1pY2FsbHkgY2hhbmdlIHRoZSB0YWIgdGl0bGUgb24gY29udGVudCBjaGFuZ2VzCi8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL2V2bzIvZnJlc2gvZWxlYWQtdjQ1L2VsZWFkX3RyYWNrL3dlYmxpbmsvd2VibGlua1RvZGF5LmFzcHgqCi8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL2V2bzIvZnJlc2gvZWxlYWQtdjQ1L2VsZWFkX3RyYWNrL3JlcG9ydHMvRGVza2xvZy9EZXNrbG9nLmFzcHgqCi8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL3J0L01lc3NlbmdlckNsaWVudC9Ib21lL0luZGV4KgovLyBAbWF0Y2ggICAgICAgIGh0dHBzOi8vd3d3LmVsZWFkY3JtLmNvbS9ldm8yL2ZyZXNoL2VsZWFkLXY0NS9lbGVhZF90cmFjay9lbGVhZF9tYWlsL01haWxib3guYXNweD9UeXBlPU0KLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIGNvbnN0IGN1c3RvbVRpdGxlID0gIkRvd250b3duIEF1dG8gQ2VudGVyIjsgLy8gWW91ciBkZXNpcmVkIHRpdGxlCiAgICBkb2N1bWVudC50aXRsZSA9IGN1c3RvbVRpdGxlOyAvLyBTZXQgaW5pdGlhbCB0aXRsZQoKICAgIC8vIEZ1bmN0aW9uIHRvIHVwZGF0ZSB0aXRsZSBpZiBpdCBjaGFuZ2VzCiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKG11dGF0aW9ucyA9PiB7CiAgICAgICAgaWYgKGRvY3VtZW50LnRpdGxlICE9PSBjdXN0b21UaXRsZSkgewogICAgICAgICAgICBkb2N1bWVudC50aXRsZSA9IGN1c3RvbVRpdGxlOwogICAgICAgIH0KICAgIH0pOwoKICAgIC8vIFN0YXJ0IG9ic2VydmluZyB0aGUgZG9jdW1lbnQgaGVhZCBmb3IgY2hhbmdlcyB0byB0aGUgdGl0bGUKICAgIG9ic2VydmVyLm9ic2VydmUoZG9jdW1lbnQucXVlcnlTZWxlY3RvcigndGl0bGUnKSwgeyBjaGlsZExpc3Q6IHRydWUgfSk7Cn0pKCk7Cg=="}, {"name": "Auto Close eLeadCRM Release Notes", "options": {"check_for_updates": true, "user_modified": 1711738521783, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/admin/releasenotesviewall.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434157, "data": {}}, "enabled": true, "position": 2, "uuid": "a809dcf3-6143-4490-bcf8-97740977fc21", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBBdXRvIENsb3NlIGVMZWFkQ1JNIFJlbGVhc2UgTm90ZXMKLy8gQG5hbWVzcGFjZSAgICBodHRwOi8vdGFtcGVybW9ua2V5Lm5ldC8KLy8gQHZlcnNpb24gICAgICAwLjEKLy8gQGRlc2NyaXB0aW9uICBBdXRvbWF0aWNhbGx5IGNsb3NlIHRoZSByZWxlYXNlIG5vdGVzIHBvcC11cCB3aW5kb3cgZnJvbSBlTGVhZENSTS4KLy8gQGF1dGhvciAgICAgICBBbGV4Ci8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL2V2bzIvZnJlc2gvZWxlYWQtdjQ1L2VsZWFkX3RyYWNrL2FkbWluL3JlbGVhc2Vub3Rlc3ZpZXdhbGwuYXNweCoKLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIC8vIENoZWNrIGlmIHRoZSBVUkwgbWF0Y2hlcyB0aGUgb25lIG9mIHRoZSBwb3AtdXAgd2luZG93CiAgICBpZiAod2luZG93LmxvY2F0aW9uLmhyZWYuaW5jbHVkZXMoJ3JlbGVhc2Vub3Rlc3ZpZXdhbGwuYXNweCcpKSB7CiAgICAgICAgd2luZG93LmNsb3NlKCk7CiAgICB9Cn0pKCk7Cg=="}, {"name": "Bypass Refresh Confirmation", "options": {"check_for_updates": true, "user_modified": 1711738527991, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434157, "data": {}}, "enabled": true, "position": 3, "uuid": "1ac1c0ec-4699-45ad-82df-6c1c31b71910", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBCeXBhc3MgUmVmcmVzaCBDb25maXJtYXRpb24KLy8gQG5hbWVzcGFjZSAgICBodHRwOi8vdGFtcGVybW9ua2V5Lm5ldC8KLy8gQHZlcnNpb24gICAgICAwLjEKLy8gQGRlc2NyaXB0aW9uICBCeXBhc3MgcmVmcmVzaCBjb25maXJtYXRpb24gb24gRjUga2V5cHJlc3MKLy8gQGF1dGhvciAgICAgICBBbGV4Ci8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL2V2bzIvZnJlc2gvZWxlYWQtdjQ1L2VsZWFkX3RyYWNrL3dlYmxpbmsvd2VibGlua1RvZGF5LmFzcHgqCi8vIEBncmFudCAgICAgICAgbm9uZQovLyA9PS9Vc2VyU2NyaXB0PT0KCihmdW5jdGlvbigpIHsKICAgICd1c2Ugc3RyaWN0JzsKCiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGZ1bmN0aW9uKGV2ZW50KSB7CiAgICAgICAgaWYgKGV2ZW50LmtleUNvZGUgPT09IDExNikgeyAvLyBGNSBrZXkgY29kZQogICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpOwogICAgICAgICAgICBsb2NhdGlvbi5yZWxvYWQodHJ1ZSk7IC8vIEZvcmNlIHJlbG9hZCB3aXRob3V0IGNhY2hlCiAgICAgICAgfQogICAgfSk7Cn0pKCk7"}, {"name": "Message Box with Dealership Info from Airtable", "options": {"check_for_updates": true, "user_modified": 1713200936046, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/NewProspects/history.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": ["airtable.com"], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434157, "data": {}}, "enabled": true, "position": 4, "uuid": "bb680026-4732-41d8-aa5d-43285f499d06", "source": "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"}, {"name": "eLeads Faster R<PERSON>tops", "options": {"check_for_updates": true, "user_modified": 1712863038640, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/*", "https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/reports/*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434157, "data": {}}, "enabled": true, "position": 5, "uuid": "a83eb108-e1b3-4b83-b9c0-5e15a7dbc248", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBlTGVhZHMgRmFzdGVyIFJvb2Z0b3BzCi8vIEB2ZXJzaW9uICAgICAgNS4wCi8vIEBkZXNjcmlwdGlvbiAgRW5oYW5jZWQgc2NyaXB0IHdpdGggcmVwb3J0aW5nCi8vIEBhdXRob3IgICAgICAgQWxleAovLyBAbWF0Y2ggICAgICAgIGh0dHBzOi8vd3d3LmVsZWFkY3JtLmNvbS9ldm8yL2ZyZXNoLyoKLy8gQG1hdGNoICAgICAgICBodHRwczovL3d3dy5lbGVhZGNybS5jb20vZXZvMi9mcmVzaC9lbGVhZC12NDUvZWxlYWRfdHJhY2svcmVwb3J0cy8qCi8vIEBncmFudCAgICAgICAgbm9uZQovLyA9PS9Vc2VyU2NyaXB0PT0KCmxldCBnbG9iYWxMZWFkVHlwZSA9ICdpbnRlcm5ldCc7IC8vIERlZmF1bHQgdG8gJ2ludGVybmV0JwpsZXQgYWdlbnROYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FnZW50TmFtZScpIHx8ICdEZWZhdWx0IEFnZW50IE5hbWUnOwoKCmRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVLZXlTaG9ydGN1dHMpOwoKZnVuY3Rpb24gaGFuZGxlS2V5U2hvcnRjdXRzKGUpIHsKICAgIGlmICgoZS5jdHJsS2V5IHx8IGUubWV0YUtleSkgJiYgZS5hbHRLZXkpIHsKICAgICAgICBzd2l0Y2ggKGUuY29kZSkgewogICAgICAgICAgICBjYXNlICdEaWdpdDUnOgogICAgICAgICAgICAgICAgaW5pdFNjcmlwdCgpOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgJ0RpZ2l0MCc6CiAgICAgICAgICAgICAgICBzZXRVc2VyQWdlbnQoKTsKICAgICAgICAgICAgICAgIGJyZWFrOwogICAgICAgIH0KICAgIH0KfQoKZnVuY3Rpb24gc2V0VXNlckFnZW50KCkgewogICAgY29uc3QgYWdlbnROYW1lID0gcHJvbXB0KCdFbnRlciB5b3VyIG5hbWU6Jyk7CiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWdlbnROYW1lJywgYWdlbnROYW1lKTsKfQoKZnVuY3Rpb24gaW5pdFNjcmlwdCgpIHsKICAgICd1c2Ugc3RyaWN0JzsKCiAgICBjb25zdCBiYXRjaFNpemUgPSAxMDsKICAgIGxldCBzdGFydFRpbWUsIGVuZFRpbWU7CiAgICBsZXQgYWdlbnROYW1lID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FnZW50TmFtZScpIHx8ICcnOwogICAgbGV0IHNlbGVjdGVkRGF5cyA9ICcnOwogICAgbGV0IHNlbGVjdGVkRGVhbGVyc2hpcCA9ICcnOwogICAgbGV0IHJvb2Z0b3BTdGFydCA9ICcnOwogICAgbGV0IHJvb2Z0b3BGaW5pc2ggPSAnJzsKICAgIGxldCBsZWFkVHlwZTsgLy8gQWRkZWQgdG8gY2FwdHVyZSBsZWFkVHlwZQoKICAgIGNvbnN0IG1ldGFSZWZyZXNoID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignbWV0YVtodHRwLWVxdWl2PSJyZWZyZXNoIl0nKTsKICAgIGlmIChtZXRhUmVmcmVzaCkgbWV0YVJlZnJlc2gucmVtb3ZlKCk7CgogICAgLy8gUmVtb3ZlIGFueSBleGlzdGluZyBpbnRlcnZhbCB0aW1lcnMKICAgIGZvciAodmFyIGkgPSAxOyBpIDwgOTk5OTk7IGkrKykgewogICAgICAgIHdpbmRvdy5jbGVhckludGVydmFsKGkpOwogICAgICAgIHdpbmRvdy5jbGVhclRpbWVvdXQoaSk7CiAgICB9CgogICAgZnVuY3Rpb24gY291bnRDdXN0b21lck5hbWVzKGxpbmtzKSB7CiAgICAgICAgY29uc3QgY291bnQgPSB7fTsKICAgICAgICBmb3IgKGxldCBsaW5rIG9mIGxpbmtzKSB7CiAgICAgICAgICAgIGNvbnN0IG5hbWUgPSBsaW5rLmlubmVyVGV4dC50cmltKCk7CiAgICAgICAgICAgIGlmIChjb3VudFtuYW1lXSkgewogICAgICAgICAgICAgICAgY291bnRbbmFtZV0rKzsKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIGNvdW50W25hbWVdID0gMTsKICAgICAgICAgICAgfQogICAgICAgIH0KICAgICAgICByZXR1cm4gY291bnQ7CiAgICB9CgogICAgZnVuY3Rpb24gbGlzdEN1c3RvbWVyTmFtZXMobGlua3MsIHN0YXJ0SW5kZXgsIGVuZEluZGV4LCBuYW1lQ291bnRzKSB7CiAgICAgICAgbGV0IGxpc3RIdG1sID0gJzx1bD4nOwogICAgICAgIGNvbnN0IGFscmVhZHlNYXJrZWQgPSB7fTsKICAgICAgICBmb3IgKGxldCBpID0gc3RhcnRJbmRleDsgaSA8IGVuZEluZGV4ICYmIGkgPCBsaW5rcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgICBjb25zdCBuYW1lID0gbGlua3NbaV0uaW5uZXJUZXh0LnRyaW0oKTsKICAgICAgICAgICAgaWYgKG5hbWVDb3VudHNbbmFtZV0gPiAxICYmICFhbHJlYWR5TWFya2VkW25hbWVdKSB7CiAgICAgICAgICAgICAgICBhbHJlYWR5TWFya2VkW25hbWVdID0gdHJ1ZTsKICAgICAgICAgICAgICAgIGxpc3RIdG1sICs9IGA8bGk+JHtuYW1lfTwvbGk+YDsKICAgICAgICAgICAgfSBlbHNlIGlmIChuYW1lQ291bnRzW25hbWVdID4gMSAmJiBhbHJlYWR5TWFya2VkW25hbWVdKSB7CiAgICAgICAgICAgICAgICBsaXN0SHRtbCArPSBgPGxpPiR7bmFtZX0gPHNwYW4gc3R5bGU9ImNvbG9yOiByZWQ7Ij4oZHVwbGljYXRlZCk8L3NwYW4+PC9saT5gOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgICAgbGlzdEh0bWwgKz0gYDxsaT4ke25hbWV9PC9saT5gOwogICAgICAgICAgICB9CiAgICAgICAgfQogICAgICAgIGxpc3RIdG1sICs9ICc8L3VsPic7CiAgICAgICAgcmV0dXJuIGxpc3RIdG1sOwogICAgfQoKICAgIGZ1bmN0aW9uIHN0YXJ0VGltZXIoKSB7CiAgICAgICAgc3RhcnRUaW1lID0gbmV3IERhdGUoKTsKICAgICAgICByb29mdG9wU3RhcnQgPSBzdGFydFRpbWUudG9Mb2NhbGVTdHJpbmcoKTsKICAgIH0KCiAgICBmdW5jdGlvbiBzdG9wVGltZXIoKSB7CiAgICAgICAgZW5kVGltZSA9IG5ldyBEYXRlKCk7CiAgICAgICAgcm9vZnRvcEZpbmlzaCA9IGVuZFRpbWUudG9Mb2NhbGVTdHJpbmcoKTsKICAgICAgICBjb25zdCB0aW1lRGlmZiA9IGVuZFRpbWUgLSBzdGFydFRpbWU7CiAgICAgICAgY29uc3QgZWxhcHNlZE1pbnV0ZXMgPSBNYXRoLnJvdW5kKHRpbWVEaWZmIC8gNjAwMDApOwogICAgICAgIHJldHVybiBlbGFwc2VkTWludXRlczsKICAgIH0KCiAgICBmdW5jdGlvbiBjcmVhdGVEaWFsb2dCb3goaHRtbENvbnRlbnQpIHsKICAgICAgICBjb25zdCBkaWFsb2dCb3ggPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTsKICAgICAgICBkaWFsb2dCb3guc3R5bGUucG9zaXRpb24gPSAnZml4ZWQnOwogICAgICAgIGRpYWxvZ0JveC5zdHlsZS50b3AgPSAnMzAlJzsKICAgICAgICBkaWFsb2dCb3guc3R5bGUubGVmdCA9ICc1MCUnOwogICAgICAgIGRpYWxvZ0JveC5zdHlsZS50cmFuc2Zvcm0gPSAndHJhbnNsYXRlKC01MCUsIC01MCUpJzsKICAgICAgICBkaWFsb2dCb3guc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJyNGOUY5RjknOwogICAgICAgIGRpYWxvZ0JveC5zdHlsZS5ib3JkZXIgPSAnMXB4IHNvbGlkICMwMDdCRkYnOwogICAgICAgIGRpYWxvZ0JveC5zdHlsZS5ib3JkZXJSYWRpdXMgPSAnMTJweCc7CiAgICAgICAgZGlhbG9nQm94LnN0eWxlLnBhZGRpbmcgPSAnNXB4JzsKICAgICAgICBkaWFsb2dCb3guc3R5bGUuekluZGV4ID0gJzk5OTknOwogICAgICAgIGRpYWxvZ0JveC5pbm5lckhUTUwgPSBodG1sQ29udGVudDsKCiAgICAgICAgY29uc3QgdGl0bGUgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdkaXYnKTsKICAgICAgICB0aXRsZS5pbm5lckhUTUwgPSAiVHJ1ZUJEQyBGYXN0ZXIgZUxlYWRzIFJvb2Z0b3BzIjsKICAgICAgICB0aXRsZS5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAnIzAwN0JGRic7CiAgICAgICAgdGl0bGUuc3R5bGUuY29sb3IgPSAnd2hpdGUnOwogICAgICAgIHRpdGxlLnN0eWxlLnBhZGRpbmcgPSAnNXB4JzsKICAgICAgICB0aXRsZS5zdHlsZS5ib3JkZXJUb3BMZWZ0UmFkaXVzID0gJzVweCc7CiAgICAgICAgdGl0bGUuc3R5bGUuYm9yZGVyVG9wUmlnaHRSYWRpdXMgPSAnNXB4JzsKICAgICAgICB0aXRsZS5zdHlsZS50ZXh0QWxpZ24gPSAnY2VudGVyJzsKCiAgICAgICAgZGlhbG9nQm94LnByZXBlbmQodGl0bGUpOwoKICAgICAgICByZXR1cm4gZGlhbG9nQm94OwogICAgfQoKZnVuY3Rpb24gc2VuZERhdGFUb0FpcnRhYmxlKGZvcm1EYXRhKSB7CiAgICAvLyBEZWZpbmUgeW91ciBBaXJ0YWJsZSBBUEkgY29uZmlndXJhdGlvbiBoZXJlCiAgICBjb25zdCBhcGlLZXkgPSAncGF0dkRhM044NE1lU21QcVMuYmVhMTg3MzFmZWU0ODUzMjViYTYxYjM2MzcxOWJmOTE4ODE1MWM5OTMyMjQ1ZDhmNDNlN2NjNTZjNjQ1ZmRmMic7CiAgICBjb25zdCBiYXNlSWQgPSAnYXBwZXhSOXRGS0dIalNXTkUnOwogICAgY29uc3QgdGFibGVOYW1lID0gJ1Jvb2Z0b3BzJzsKCiAgICAvLyBEZWZpbmUgdGhlIEFpcnRhYmxlIEFQSSBlbmRwb2ludAogICAgY29uc3QgYXBpVXJsID0gYGh0dHBzOi8vYXBpLmFpcnRhYmxlLmNvbS92MC8ke2Jhc2VJZH0vJHt0YWJsZU5hbWV9YDsKCiAgICAvLyBDcmVhdGUgYSBoZWFkZXJzIG9iamVjdCB3aXRoIHlvdXIgQVBJIGtleQogICAgY29uc3QgaGVhZGVycyA9IHsKICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHthcGlLZXl9YCwKICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLAogICAgfTsKCiAgICAvLyBDaGVjayBpZiB0aGUgdHdvIHNlbGVjdGVkIGRheXMgYXJlIHRoZSBzYW1lIGFuZCBtb2RpZnkgdGhlIERheXNfV29ya2VkIGZpZWxkIGFjY29yZGluZ2x5CiAgICBjb25zdCBkYXlzV29ya2VkID0gZm9ybURhdGEuRGF5c19Xb3JrZWQuc3BsaXQoJywnKS5tYXAoZGF5ID0+IGRheS50cmltKCkpOwogICAgaWYgKGRheXNXb3JrZWQubGVuZ3RoID09PSAyKSB7CiAgICAgICAgY29uc3QgZGF5MSA9IHBhcnNlSW50KGRheXNXb3JrZWRbMF0pOwogICAgICAgIGNvbnN0IGRheTIgPSBwYXJzZUludChkYXlzV29ya2VkWzFdKTsKICAgICAgICBpZiAoZGF5MSA9PT0gZGF5MikgewogICAgICAgICAgICBmb3JtRGF0YS5EYXlzX1dvcmtlZCA9IGRheTEudG9TdHJpbmcoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zdCBhbGxEYXlzID0gW107CiAgICAgICAgICAgIGZvciAobGV0IGkgPSBkYXkxOyBpIDw9IGRheTI7IGkrKykgewogICAgICAgICAgICAgICAgYWxsRGF5cy5wdXNoKGkpOwogICAgICAgICAgICB9CiAgICAgICAgICAgIGZvcm1EYXRhLkRheXNfV29ya2VkID0gYWxsRGF5cy5qb2luKCcsICcpOwogICAgICAgIH0KICAgIH0gZWxzZSB7CiAgICAgICAgZm9ybURhdGEuRGF5c19Xb3JrZWQgPSBkYXlzV29ya2VkLmpvaW4oJywgJyk7CiAgICB9CgogICAgLy8gQ29udmVydCBBcHBvaW50bWVudHMgdG8gYSBudW1iZXIKICAgIGZvcm1EYXRhLkFwcG9pbnRtZW50cyA9IHBhcnNlSW50KGZvcm1EYXRhLkFwcG9pbnRtZW50cyk7CgogICAgZmV0Y2goYXBpVXJsLCB7CiAgICAgICAgbWV0aG9kOiAnUE9TVCcsCiAgICAgICAgaGVhZGVyczogaGVhZGVycywKICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICAgIGZpZWxkczogewogICAgICAgICAgICAgICAgJ0FnZW50JzogZm9ybURhdGEuQWdlbnQsCiAgICAgICAgICAgICAgICAnRGVhbGVyc2hpcCc6IGZvcm1EYXRhLkRlYWxlcnNoaXAsCiAgICAgICAgICAgICAgICAnRGF5c19Xb3JrZWQnOiBmb3JtRGF0YS5EYXlzX1dvcmtlZCwKICAgICAgICAgICAgICAgICdSb29mdG9wX1N0YXJ0JzogZm9ybURhdGEuUm9vZnRvcF9TdGFydCwKICAgICAgICAgICAgICAgICdSb29mdG9wX0ZpbmlzaCc6IGZvcm1EYXRhLlJvb2Z0b3BfRmluaXNoLAogICAgICAgICAgICAgICAgJ1RvdGFsX0xlYWRzJzogZm9ybURhdGEuVG90YWxfTGVhZHMsCiAgICAgICAgICAgICAgICAnQXBwb2ludG1lbnRzJzogZm9ybURhdGEuQXBwb2ludG1lbnRzLAogICAgICAgICAgICB9CiAgICAgICAgfSksCiAgICB9KQogICAgLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGlmIChyZXNwb25zZS5vaykgewogICAgICAgICAgICBjb25zb2xlLmxvZygnUmVjb3JkIGNyZWF0ZWQgaW4gQWlydGFibGU6JywgcmVzcG9uc2UpOwogICAgICAgICAgICBhbGVydCgiQXBwb2ludG1lbnQgaW5mb3JtYXRpb24gc2VudCBzdWNjZXNzZnVsbHkhIik7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNyZWF0ZSByZWNvcmQgaW4gQWlydGFibGU6JywgcmVzcG9uc2UpOwogICAgICAgICAgICBhbGVydCgiRmFpbGVkIHRvIHNlbmQgYXBwb2ludG1lbnQgaW5mb3JtYXRpb24uIik7CiAgICAgICAgfQogICAgfSkKICAgIC5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2VuZGluZyBkYXRhIHRvIEFpcnRhYmxlOicsIGVycm9yKTsgLy8gTG9nIHRoZSBlcnJvcgogICAgICAgIGFsZXJ0KGBBbiBlcnJvciBvY2N1cnJlZCB3aGlsZSBzZW5kaW5nIGRhdGE6ICR7ZXJyb3IubWVzc2FnZX1gKTsKICAgIH0pOwp9CgpmdW5jdGlvbiBzaG93UmVwb3J0KHRvdGFsTGlua3MsIGNsaWNrZWRMaW5rcywgdW5vcGVuZWRMaW5rcywgYXBwdHMpIHsKICAgIGNvbnN0IGVsYXBzZWRUaW1lID0gc3RvcFRpbWVyKCk7CgogICAgLy8gQ29udGVudCB0byBiZSBkaXNwbGF5ZWQgaW4gdGhlIGRpYWxvZyBib3gKICAgIGNvbnN0IHJlcG9ydFRpdGxlID0gZ2xvYmFsTGVhZFR5cGUgPT09ICdpbnRlcm5ldCcgPyAnSW50ZXJuZXQgTGVhZHMgUm9vZnRvcCBSZXBvcnQnIDogJ1Bob25lIFVwIExlYWRzIFJvb2Z0b3AgUmVwb3J0JzsKICAgIGNvbnN0IGRpc3BsYXlDb250ZW50ID0gYAogICAgICAgIDxkaXYgY2xhc3M9InJlcG9ydC10aXRsZSI+JHtyZXBvcnRUaXRsZX08L2Rpdj4KICAgICAgICA8ZGl2PkFnZW50OiA8c3BhbiBjbGFzcz0iYWdlbnQtbmFtZSI+JHthZ2VudE5hbWV9PC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXY+RGVhbGVyc2hpcDogPHNwYW4gY2xhc3M9InNlbGVjdGVkLWRlYWxlcnNoaXAiPiR7c2VsZWN0ZWREZWFsZXJzaGlwfTwvc3Bhbj48L2Rpdj4KICAgICAgICA8ZGl2PkRheXMgV29ya2VkOiA8c3BhbiBjbGFzcz0ic2VsZWN0ZWQtZGF5cyI+JHtzZWxlY3RlZERheXN9PC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXY+Um9vZnRvcCBTdGFydDogPHNwYW4gY2xhc3M9InJvb2Z0b3Atc3RhcnQiPiR7cm9vZnRvcFN0YXJ0fTwvc3Bhbj48L2Rpdj4KICAgICAgICA8ZGl2PlJvb2Z0b3AgRmluaXNoOiA8c3BhbiBjbGFzcz0icm9vZnRvcC1maW5pc2giPiR7cm9vZnRvcEZpbmlzaH08L3NwYW4+PC9kaXY+CiAgICAgICAgPGRpdj5Ub3RhbCBMZWFkcy9MaW5rcyBPcGVuZWQ6IDxzcGFuIGNsYXNzPSJjbGlja2VkLWxpbmtzIj4ke2NsaWNrZWRMaW5rc308L3NwYW4+PC9kaXY+CiAgICAgICAgPGRpdj5MZWFkcy9MaW5rcyBMZWZ0OiA8c3BhbiBjbGFzcz0idW5vcGVuZWQtbGlua3MiPiR7dW5vcGVuZWRMaW5rc308L3NwYW4+PC9kaXY+CiAgICAgICAgPGRpdj5Ub3RhbCBUaW1lIFRha2VuOiA8c3BhbiBjbGFzcz0iZWxhcHNlZC10aW1lIj4ke2VsYXBzZWRUaW1lfSBtaW51dGVzPC9zcGFuPjwvZGl2PgogICAgICAgIDxkaXY+QXBwb2ludG1lbnRzOiA8c3BhbiBjbGFzcz0iYXBwdHMiPiR7YXBwdHN9PC9zcGFuPjwvZGl2PgogICAgYDsKCiAgICAvLyBDb250ZW50IHRvIGJlIGNvcGllZCB0byB0aGUgY2xpcGJvYXJkCiAgICBjb25zdCB0ZXh0VG9Db3B5ID0gYAoke3JlcG9ydFRpdGxlfQpBZ2VudDogJHthZ2VudE5hbWV9CkRlYWxlcnNoaXA6ICR7c2VsZWN0ZWREZWFsZXJzaGlwfQpEYXlzIFdvcmtlZDogJHtzZWxlY3RlZERheXN9ClJvb2Z0b3AgU3RhcnQ6ICR7cm9vZnRvcFN0YXJ0fQpSb29mdG9wIEZpbmlzaDogJHtyb29mdG9wRmluaXNofQpUb3RhbCBMZWFkcy9MaW5rcyBPcGVuZWQ6ICR7Y2xpY2tlZExpbmtzfQpMZWFkcy9MaW5rcyBMZWZ0OiAke3Vub3BlbmVkTGlua3N9ClRvdGFsIFRpbWUgVGFrZW46ICR7ZWxhcHNlZFRpbWV9IG1pbnV0ZXMKQXBwb2ludG1lbnRzOiAke2FwcHRzfQogICAgYC50cmltKCk7IC8vIFVzZSAudHJpbSgpIHRvIHJlbW92ZSBsZWFkaW5nL3RyYWlsaW5nIHdoaXRlc3BhY2UKCiAgICAvLyBDcmVhdGUgdGhlIGRpYWxvZyBib3ggd2l0aCB0aGUgZGlzcGxheSBjb250ZW50IGFuZCBidXR0b25zCiAgICBjb25zdCBkaWFsb2dCb3ggPSBjcmVhdGVEaWFsb2dCb3goZGlzcGxheUNvbnRlbnQgKyBgCiAgICAgICAgPGJ1dHRvbiBpZD0iY29weUJ0biIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICMyOGE3NDU7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiA4cHg7Ij5Db3B5IHRvIENsaXBib2FyZDwvYnV0dG9uPgogICAgICAgIDxidXR0b24gaWQ9ImNsb3NlUmVwb3J0QnRuIiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjogI2RjMzU0NTsgY29sb3I6IHdoaXRlOyBib3JkZXItcmFkaXVzOiA2cHg7IHBhZGRpbmc6IDhweDsiPkNsb3NlIFJlcG9ydDwvYnV0dG9uPgogICAgYCk7CgogICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChkaWFsb2dCb3gpOwoKICAgIC8vIEFkZCB0aGUgY2xpY2sgZXZlbnQgbGlzdGVuZXIgdG8gdGhlICJDb3B5IHRvIENsaXBib2FyZCIgYnV0dG9uCiAgICBjb25zdCBjb3B5QnRuID0gZGlhbG9nQm94LnF1ZXJ5U2VsZWN0b3IoJyNjb3B5QnRuJyk7CiAgICBjb3B5QnRuLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgKCkgPT4gewogICAgICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRleHRUb0NvcHkpLnRoZW4oKCkgPT4gewogICAgICAgICAgICBhbGVydCgnUmVwb3J0IGNvcGllZCB0byBjbGlwYm9hcmQuJyk7CiAgICAgICAgfSkuY2F0Y2goZXJyID0+IHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcignQ291bGQgbm90IGNvcHkgdGV4dDogJywgZXJyKTsKICAgICAgICB9KTsKCiAgICAgICAgLy8gU2VuZCB0aGUgZGF0YSB0byBBaXJ0YWJsZSAoQWdlbnQgYW5kIERlYWxlcnNoaXApCiAgICAgICAgY29uc3QgZm9ybURhdGEgPSB7CiAgICAgICAgICAgICdBZ2VudCc6IGFnZW50TmFtZSwKICAgICAgICAgICAgJ0RlYWxlcnNoaXAnOiBzZWxlY3RlZERlYWxlcnNoaXAsCiAgICAgICAgICAgICdEYXlzX1dvcmtlZCc6IHNlbGVjdGVkRGF5cywKICAgICAgICAgICAgJ1Jvb2Z0b3BfU3RhcnQnOiByb29mdG9wU3RhcnQsCiAgICAgICAgICAgICdSb29mdG9wX0ZpbmlzaCc6IHJvb2Z0b3BGaW5pc2gsCiAgICAgICAgICAgICdUb3RhbF9MZWFkcyc6IGNsaWNrZWRMaW5rcy50b1N0cmluZygpLAogICAgICAgICAgICAnQXBwb2ludG1lbnRzJzogYXBwdHMsCiAgICAgICAgfTsKICAgICAgICBzZW5kRGF0YVRvQWlydGFibGUoZm9ybURhdGEpOwogICAgfSk7CgogICAgLy8gQWRkIHRoZSBjbGljayBldmVudCBsaXN0ZW5lciB0byB0aGUgIkNsb3NlIFJlcG9ydCIgYnV0dG9uCiAgICBjb25zdCBjbG9zZVJlcG9ydEJ0biA9IGRpYWxvZ0JveC5xdWVyeVNlbGVjdG9yKCcjY2xvc2VSZXBvcnRCdG4nKTsKICAgIGNsb3NlUmVwb3J0QnRuLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgKCkgPT4gewogICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoZGlhbG9nQm94KTsKICAgIH0pOwp9CiAgICBmdW5jdGlvbiBjbGlja05leHRCYXRjaChsaW5rcywgc3RhcnRJbmRleCwgbmFtZUNvdW50cywgY2xpY2tlZExpbmtzKSB7CiAgICAgICAgZm9yIChsZXQgaSA9IHN0YXJ0SW5kZXg7IGkgPCBzdGFydEluZGV4ICsgYmF0Y2hTaXplICYmIGkgPCBsaW5rcy5sZW5ndGg7IGkrKykgewogICAgICAgICAgICBsaW5rc1tpXS50YXJnZXQgPSAiX2JsYW5rIjsKICAgICAgICAgICAgY29uc3QgZXZlbnQgPSBuZXcgTW91c2VFdmVudCgnY2xpY2snLCB7CiAgICAgICAgICAgICAgICB2aWV3OiB3aW5kb3csCiAgICAgICAgICAgICAgICBidWJibGVzOiB0cnVlLAogICAgICAgICAgICAgICAgY2FuY2VsYWJsZTogdHJ1ZQogICAgICAgICAgICB9KTsKICAgICAgICAgICAgbGlua3NbaV0uZGlzcGF0Y2hFdmVudChldmVudCk7CiAgICAgICAgICAgIGNsaWNrZWRMaW5rcysrOwogICAgICAgIH0KCiAgICAgICAgY29uc3QgdW5vcGVuZWRMaW5rcyA9IGxpbmtzLmxlbmd0aCAtIGNsaWNrZWRMaW5rczsKICAgICAgICBpZiAoc3RhcnRJbmRleCArIGJhdGNoU2l6ZSA8IGxpbmtzLmxlbmd0aCkgewogICAgICAgICAgICBjb25zdCBkaWFsb2dCb3ggPSBjcmVhdGVEaWFsb2dCb3goYERvIHlvdSB3YW50IHRvIGNvbnRpbnVlIHdpdGggdGhlIG5leHQgYmF0Y2g/ICR7bGlzdEN1c3RvbWVyTmFtZXMobGlua3MsIHN0YXJ0SW5kZXggKyBiYXRjaFNpemUsIHN0YXJ0SW5kZXggKyAyICogYmF0Y2hTaXplLCBuYW1lQ291bnRzKX0KICAgICAgICAgICAgPGRpdj5MZWFkcy9MaW5rcyBMZWZ0OiAke3Vub3BlbmVkTGlua3N9PC9kaXY+CiAgICAgICAgICAgIDxidXR0b24gaWQ9ImNhbmNlbEJ0biIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNkYzM1NDU7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxMHB4OyI+Q2FuY2VsPC9idXR0b24+CiAgICAgICAgICAgIDxidXR0b24gaWQ9ImZpbmlzaEJ0biIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmMxMDc7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiAxMHB4OyI+RmluaXNoPC9idXR0b24+CiAgICAgICAgICAgIDxidXR0b24gaWQ9ImNvbnRpbnVlQnRuIiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjogIzI4YTc0NTsgY29sb3I6IHdoaXRlOyBib3JkZXItcmFkaXVzOiA2cHg7IHBhZGRpbmc6IDEwcHg7Ij5Db250aW51ZSB3aXRoIHRoZSBuZXh0IGJhdGNoPC9idXR0b24+YCk7CiAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoZGlhbG9nQm94KTsKCiAgICAgICAgICAgIGNvbnN0IGNhbmNlbEJ0biA9IGRpYWxvZ0JveC5xdWVyeVNlbGVjdG9yKCcjY2FuY2VsQnRuJyk7CiAgICAgICAgICAgIGNhbmNlbEJ0bi5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsICgpID0+IHsKICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoZGlhbG9nQm94KTsKICAgICAgICAgICAgfSk7CgogICAgICAgICAgICBjb25zdCBmaW5pc2hCdG4gPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2ZpbmlzaEJ0bicpOwogICAgICAgICAgICBmaW5pc2hCdG4uYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoKSA9PiB7CiAgICAgICAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGRpYWxvZ0JveCk7CgogICAgICAgICAgICAgICAgY29uc3QgYXBwdERpYWxvZ0JveCA9IGNyZWF0ZURpYWxvZ0JveChgRW50ZXIgeW91ciBBcHB0IGNvdW50PC9icj5pZiBub25lLCB0eXBlIDAgYW5kIGNsaWNrIENvbmZpcm0gb25jZSB5b3UgYXJlIGRvbmUuCiAgICAgICAgICAgICAgICAgICAgPGlucHV0IHR5cGU9Im51bWJlciIgaWQ9ImFwcHRzIiBwbGFjZWhvbGRlcj0iMCIgbWluPSIwIiBtYXg9Ijk5OSIgc3R5bGU9IndpZHRoOiA2MHB4OyI+CiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBpZD0iZG9uZUJ0biIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICMyOGE3NDU7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiA1cHg7Ij5Db25maXJtIERvbmU8L2J1dHRvbj5gKTsKICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoYXBwdERpYWxvZ0JveCk7CgogICAgICAgICAgICAgICAgY29uc3QgZG9uZUJ0biA9IGFwcHREaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2RvbmVCdG4nKTsKICAgICAgICAgICAgICAgIGRvbmVCdG4uYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoKSA9PiB7CiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXBwdHMgPSBhcHB0RGlhbG9nQm94LnF1ZXJ5U2VsZWN0b3IoJyNhcHB0cycpLnZhbHVlOwogICAgICAgICAgICAgICAgICAgIGlmIChhcHB0cyA9PT0gJycgfHwgaXNOYU4oYXBwdHMpKSB7CiAgICAgICAgICAgICAgICAgICAgICAgIGFsZXJ0KCdQbGVhc2UgZW50ZXIgYSB2YWxpZCBudW1iZXIgaW4gdGhlIEFwcHRzIGZpZWxkLicpOwogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47CiAgICAgICAgICAgICAgICAgICAgfQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoYXBwdERpYWxvZ0JveCk7CiAgICAgICAgICAgICAgICAgICAgc2hvd1JlcG9ydChsaW5rcy5sZW5ndGgsIGNsaWNrZWRMaW5rcywgdW5vcGVuZWRMaW5rcywgYXBwdHMpOwogICAgICAgICAgICAgICAgfSk7CiAgICAgICAgICAgIH0pOwoKICAgICAgICAgICAgY29uc3QgY29udGludWVCdG4gPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2NvbnRpbnVlQnRuJyk7CiAgICAgICAgICAgIGNvbnRpbnVlQnRuLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgKCkgPT4gewogICAgICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChkaWFsb2dCb3gpOwogICAgICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiBjbGlja05leHRCYXRjaChsaW5rcywgc3RhcnRJbmRleCArIGJhdGNoU2l6ZSwgbmFtZUNvdW50cywgY2xpY2tlZExpbmtzKSwgMTAwMCk7CiAgICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnN0IGRpYWxvZ0JveCA9IGNyZWF0ZURpYWxvZ0JveChgQWxsIGxpbmtzIGhhdmUgYmVlbiBvcGVuZWQuIEVudGVyIHlvdXIgQXBwdCBjb3VudDwvYnI+aWYgbm9uZSwgdHlwZSAwIGFuZCBjbGljayBDb25maXJtIG9uY2UgeW91IGFyZSBkb25lLgogICAgICAgICAgICA8aW5wdXQgdHlwZT0ibnVtYmVyIiBpZD0iYXBwdHMiIHBsYWNlaG9sZGVyPSIwIiBtaW49IjAiIG1heD0iOTk5IiBzdHlsZT0id2lkdGg6IDYwcHg7Ij4KICAgICAgICAgICAgPGJ1dHRvbiBpZD0iZG9uZUJ0biIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICMyOGE3NDU7IGNvbG9yOiB3aGl0ZTsgYm9yZGVyLXJhZGl1czogNnB4OyBwYWRkaW5nOiA1cHg7Ij5Db25maXJtIERvbmU8L2J1dHRvbj5gKTsKICAgICAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChkaWFsb2dCb3gpOwoKICAgICAgICAgICAgY29uc3QgZG9uZUJ0biA9IGRpYWxvZ0JveC5xdWVyeVNlbGVjdG9yKCcjZG9uZUJ0bicpOwogICAgICAgICAgICBkb25lQnRuLmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgKCkgPT4gewogICAgICAgICAgICAgICAgY29uc3QgYXBwdHMgPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2FwcHRzJykudmFsdWU7CiAgICAgICAgICAgICAgICBpZiAoYXBwdHMgPT09ICcnIHx8IGlzTmFOKGFwcHRzKSkgewogICAgICAgICAgICAgICAgICAgIGFsZXJ0KCdQbGVhc2UgZW50ZXIgYSB2YWxpZCBudW1iZXIgaW4gdGhlIEFwcHRzIGZpZWxkLicpOwogICAgICAgICAgICAgICAgICAgIHJldHVybjsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQoZGlhbG9nQm94KTsKICAgICAgICAgICAgICAgIHNob3dSZXBvcnQobGlua3MubGVuZ3RoLCBjbGlja2VkTGlua3MsIHVub3BlbmVkTGlua3MsIGFwcHRzKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgfQogICAgfQoKZnVuY3Rpb24gbWFpbkxvZ2ljKGxpbmtzKSB7CiAgICBjb25zdCBuYW1lQ291bnRzID0gY291bnRDdXN0b21lck5hbWVzKGxpbmtzKTsKCiAgICAvLyBDb21iaW5lIGZldGNoIHJlcXVlc3RzIGZvciBkZWFsZXJzaGlwIGFuZCBhZ2VudCBuYW1lcyB1c2luZyBQcm9taXNlLmFsbAogICAgUHJvbWlzZS5hbGwoWwogICAgZmV0Y2goJ2h0dHBzOi8vZ2lzdC5naXRodWJ1c2VyY29udGVudC5jb20vZ2VvbW9kaS84MjcxN2FmM2M0MzM4MWM4NTQzYzY0MjkzZWQwMTJjZS9yYXcvZ2lzdGZpbGUxLnR4dCcpLnRoZW4ocmVzcG9uc2UgPT4gcmVzcG9uc2UudGV4dCgpKSwKICAgIGZldGNoKCdodHRwczovL2dpc3QuZ2l0aHVidXNlcmNvbnRlbnQuY29tL2dlb21vZGkvZGYzYzM5YzU2MzA2NzZmMGJjZDQ0OGFhNWUwOTI2YjYvcmF3L2dpc3RmaWxlMS50eHQnKS50aGVuKHJlc3BvbnNlID0+IHJlc3BvbnNlLnRleHQoKSkKXSkudGhlbigoW2RlYWxlcnNoaXBUZXh0LCBhZ2VudFRleHRdKSA9PiB7CiAgICAgICAgY29uc3QgZGVhbGVyc2hpcHMgPSBkZWFsZXJzaGlwVGV4dC5zcGxpdCgnXG4nKS5maWx0ZXIoQm9vbGVhbik7CiAgICAgICAgY29uc3QgZGVhbGVyc2hpcERyb3Bkb3duSFRNTCA9IGA8c2VsZWN0IGlkPSJkZWFsZXJzaGlwIj4KICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iIj5TZWxlY3QgRGVhbGVyc2hpcDwvb3B0aW9uPgogICAgICAgICAgICAke2RlYWxlcnNoaXBzLm1hcChkZWFsZXJzaGlwID0+IGA8b3B0aW9uIHZhbHVlPSIke2RlYWxlcnNoaXB9Ij4ke2RlYWxlcnNoaXB9PC9vcHRpb24+YCkuam9pbignJyl9CiAgICAgICAgPC9zZWxlY3Q+YDsKCiAgICAgICAgY29uc3QgYWdlbnROYW1lcyA9IGFnZW50VGV4dC5zcGxpdCgnXG4nKS5maWx0ZXIoQm9vbGVhbik7CiAgICAgICAgY29uc3QgYWdlbnREcm9wZG93bkhUTUwgPSBgPHNlbGVjdCBpZD0iYWdlbnROYW1lIj4KICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iIj5TZWxlY3QgQWdlbnQ8L29wdGlvbj4KICAgICAgICAgICAgJHthZ2VudE5hbWVzLm1hcChuYW1lID0+IGA8b3B0aW9uIHZhbHVlPSIke25hbWV9Ij4ke25hbWV9PC9vcHRpb24+YCkuam9pbignJyl9CiAgICAgICAgPC9zZWxlY3Q+YDsKCiAgICAgICAgbGV0IGFnZW50TmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZ2VudE5hbWUnKSB8fCAnVW5rbm93bic7IC8vIFJldHJpZXZlIHRoZSBhZ2VudCdzIG5hbWUgZnJvbSBsb2NhbCBzdG9yYWdlLCBmYWxsYmFjayB0byAnVW5rbm93bicKCiAgICAgICAgY29uc3QgZGF5U2VsZWN0SFRNTCA9IGAKICAgICAgICAgICAgPGRpdj48c3Ryb25nPkxlYWQgVHlwZSBzZXQgdG8gSW50ZXJuZXQ8L3N0cm9uZz4gPHNwYW4gc3R5bGU9Im1hcmdpbi1sZWZ0OiAyMHB4OyI+QXNzaWduZWQgUmVwOiA8c3BhbiBjbGFzcz0iYXNzaWduZWQtcmVwIj4ke2FnZW50TmFtZX08L3NwYW4+PC9zcGFuPjwvZGl2PgogICAgICAgICAgICA8bGFiZWw+PGlucHV0IHR5cGU9InJhZGlvIiBuYW1lPSJsZWFkVHlwZSIgdmFsdWU9ImludGVybmV0IiBjaGVja2VkPiBJZiBpdCdzIHRoZSBmaXJzdCB0aW1lIHNldHRpbmcgeW91ciBuYW1lLCByZXN0YXJ0IHRvIHByb3Blcmx5IHNldCBpdC48L2xhYmVsPgoKICAgICAgICA8ZGl2PgogICAgICAgICAgICA8c2VsZWN0IGlkPSJkYXkxIj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IiI+U2VsZWN0IERheSAxPC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSIxIj4xPC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSIyIj4yPC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSIzIj4zPC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI0Ij40PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI1Ij41PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI2Ij42PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI3Ij43PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI4Ij44PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSI5Ij45PC9vcHRpb24+CiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSIxMCI+MTA8L29wdGlvbj4KICAgICAgICAgICAgPC9zZWxlY3Q+CiAgICAgICAgICAgIDxzZWxlY3QgaWQ9ImRheTIiPgogICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iIj5TZWxlY3QgRGF5IDI8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjEiPjE8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjIiPjI8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjMiPjM8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjQiPjQ8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjUiPjU8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjYiPjY8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjciPjc8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjgiPjg8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjkiPjk8L29wdGlvbj4KICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IjEwIj4xMDwvb3B0aW9uPgogICAgICAgICAgICA8L3NlbGVjdD4KICAgICAgICA8L2Rpdj5gOwoKICAgICAgICAvLyBOb3cgY3JlYXRlIHRoZSBkaWFsb2cgYm94IHdpdGggdGhlIGZldGNoZWQgZGF0YQogICAgICAgIGNvbnN0IGRpYWxvZ0JveCA9IGNyZWF0ZURpYWxvZ0JveChgRm91bmQgJHtsaW5rcy5sZW5ndGh9IEN1c3RvbWVycy4gU3RhcnQgd2l0aCB0aGUgZmlyc3QgYmF0Y2ggb2YgMTA/ICR7bGlzdEN1c3RvbWVyTmFtZXMobGlua3MsIDAsIGJhdGNoU2l6ZSwgbmFtZUNvdW50cyl9CiAgICAgICAgICAgICR7YWdlbnREcm9wZG93bkhUTUx9CiAgICAgICAgICAgICR7ZGF5U2VsZWN0SFRNTH0KICAgICAgICAgICAgJHtkZWFsZXJzaGlwRHJvcGRvd25IVE1MfQogICAgICAgICAgICA8YnV0dG9uIGlkPSJjYW5jZWxCdG4iIG9uY2xpY2s9ImRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQodGhpcy5jbG9zZXN0KCdkaXYnKSkiIHN0eWxlPSJiYWNrZ3JvdW5kLWNvbG9yOiAjZGMzNTQ1OyBjb2xvcjogd2hpdGU7IGJvcmRlci1yYWRpdXM6IDZweDsgcGFkZGluZzogNXB4OyI+Q2FuY2VsPC9idXR0b24+CiAgICAgICAgICAgIDxidXR0b24gaWQ9InN0YXJ0QnRuIiBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjogIzI4YTc0NTsgY29sb3I6IHdoaXRlOyBib3JkZXItcmFkaXVzOiA2cHg7IHBhZGRpbmc6IDVweDsiPlN0YXJ0IGNsaWNraW5nPC9idXR0b24+YCk7CiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChkaWFsb2dCb3gpOwoKICAgICAgICAvLyBGaW5kIHRoZSBhZ2VudCBkcm9wZG93biBhbmQgdGhlICJBc3NpZ25lZCBSZXAiIGRpc3BsYXkgc3BhbiBpbiB0aGUgZGlhbG9nIGJveAogICAgICAgIGNvbnN0IGFnZW50RHJvcGRvd24gPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2FnZW50TmFtZScpOwoKICAgICAgICBjb25zdCBhc3NpZ25lZFJlcERpc3BsYXkgPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignLmFzc2lnbmVkLXJlcCcpOyAvLyBFbnN1cmUgeW91IGhhdmUgdGhpcyBjbGFzcyBpbiB5b3VyIEhUTUwKCiAgICAvLyBTZXQgYSB0aW1lb3V0IHRvIHdhaXQgZm9yIHRoZSBkcm9wZG93biB0byBiZSBwb3B1bGF0ZWQKICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgIGNvbnN0IGFnZW50RHJvcGRvd24gPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2FnZW50TmFtZScpOwogICAgICAgIGNvbnN0IGFnZW50TmFtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdhZ2VudE5hbWUnKSB8fCAnJzsKICAgICAgICBsZXQgZm91bmRBZ2VudCA9IGZhbHNlOwoKICAgICAgICAvLyBDaGVjayBlYWNoIG9wdGlvbiBpbiB0aGUgZHJvcGRvd24gdG8gZmluZCBhIG1hdGNoCiAgICAgICAgZm9yIChjb25zdCBvcHRpb24gb2YgYWdlbnREcm9wZG93bi5vcHRpb25zKSB7CiAgICAgICAgICAgIGlmIChvcHRpb24udmFsdWUgPT09IGFnZW50TmFtZSkgewogICAgICAgICAgICAgICAgb3B0aW9uLnNlbGVjdGVkID0gdHJ1ZTsKICAgICAgICAgICAgICAgIGZvdW5kQWdlbnQgPSB0cnVlOwogICAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIH0KICAgICAgICB9CgogICAgICAgIC8vIElmIG5vIG1hdGNoaW5nIGFnZW50IG5hbWUgaXMgZm91bmQsIGluZm9ybSB0aGUgdXNlciBhbmQgdXNlIHRoZSBkZWZhdWx0IG9wdGlvbgogICAgICAgIGlmICghZm91bmRBZ2VudCAmJiBhZ2VudE5hbWUpIHsKICAgICAgICAgICAgY29uc29sZS53YXJuKGBBZ2VudCBuYW1lICcke2FnZW50TmFtZX0nIG5vdCBmb3VuZCBpbiB0aGUgZHJvcGRvd24sIHVzaW5nIGRlZmF1bHQuYCk7CiAgICAgICAgICAgIGFsZXJ0KGBBZ2VudCBuYW1lICcke2FnZW50TmFtZX0nIG5vdCBmb3VuZCwgdXNpbmcgdGhlIGRlZmF1bHQgc2VsZWN0aW9uLmApOwogICAgICAgICAgICAvLyBPcHRpb25hbGx5LCBzZXQgYSBkZWZhdWx0IG9wdGlvbiBhcyBzZWxlY3RlZCBoZXJlCiAgICAgICAgfQogICAgfSwgNTAwKTsgLy8gQWRqdXN0IHRoZSB0aW1lb3V0IGFzIG5lY2Vzc2FyeQogICAgICAgIC8vIEF0dGFjaCB0aGUgY2hhbmdlIGV2ZW50IGxpc3RlbmVyIHRvIHRoZSBhZ2VudCBkcm9wZG93bgphZ2VudERyb3Bkb3duLmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIChldmVudCkgPT4gewogICAgYWdlbnROYW1lID0gZXZlbnQudGFyZ2V0LnZhbHVlOyAvLyBVcGRhdGUgdGhlIGdsb2JhbCB2YXJpYWJsZQogICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FnZW50TmFtZScsIGFnZW50TmFtZSk7IC8vIFVwZGF0ZSBsb2NhbCBzdG9yYWdlCgogICAgLy8gVXBkYXRlIGFueSBVSSBlbGVtZW50cyBvciB2YXJpYWJsZXMgZGVwZW5kZW50IG9uIGFnZW50TmFtZQogICAgaWYgKGFzc2lnbmVkUmVwRGlzcGxheSkgewogICAgICAgIGFzc2lnbmVkUmVwRGlzcGxheS50ZXh0Q29udGVudCA9IGFnZW50TmFtZTsKICAgIH0KfSk7CgogICAgICAgIC8vIEV2ZW50IGxpc3RlbmVyIGZvciB0aGUgc3RhcnQgYnV0dG9uCiAgICAgICAgY29uc3Qgc3RhcnRCdG4gPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI3N0YXJ0QnRuJyk7CiAgICAgICAgc3RhcnRCdG4uYWRkRXZlbnRMaXN0ZW5lcignY2xpY2snLCAoKSA9PiB7CiAgICAgICAgICAgIGNvbnN0IHNlbGVjdGVkQWdlbnROYW1lID0gYWdlbnREcm9wZG93bi52YWx1ZTsgLy8gRGlyZWN0bHkgZ2V0IHRoZSB2YWx1ZSBmcm9tIHRoZSBkcm9wZG93bgogICAgICAgICAgICBjb25zdCBkYXkxID0gZGlhbG9nQm94LnF1ZXJ5U2VsZWN0b3IoJyNkYXkxJykudmFsdWU7CiAgICAgICAgICAgIGNvbnN0IGRheTIgPSBkaWFsb2dCb3gucXVlcnlTZWxlY3RvcignI2RheTInKS52YWx1ZTsKICAgICAgICAgICAgc2VsZWN0ZWREZWFsZXJzaGlwID0gZGlhbG9nQm94LnF1ZXJ5U2VsZWN0b3IoJyNkZWFsZXJzaGlwJykudmFsdWU7CgogICAgICAgIC8vIENoZWNrIGlmIGFuIGFnZW50IG5hbWUgaGFzIGJlZW4gc2VsZWN0ZWQKICAgICAgICBpZiAoIXNlbGVjdGVkQWdlbnROYW1lIHx8IHNlbGVjdGVkQWdlbnROYW1lID09PSAiIikgeyAvLyBBZGp1c3QgY29uZGl0aW9uIGFzIG5lZWRlZCBiYXNlZCBvbiB5b3VyIGRyb3Bkb3duJ3MgZGVmYXVsdCB2YWx1ZQogICAgICAgICAgICBhbGVydCgnWW91IG11c3Qgc2VsZWN0IGFuIGFnZW50IGZyb20gdGhlIGRyb3Bkb3duIHRvIHN0YXJ0LicpOwogICAgICAgICAgICByZXR1cm47IC8vIFByZXZlbnQgZnVydGhlciBleGVjdXRpb24KICAgICAgICB9CgogICAgICAgLy8gU2luY2UgYW4gYWdlbnQgbmFtZSBpcyBzZWxlY3RlZCwgdXBkYXRlIGxvY2FsU3RvcmFnZQogICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2FnZW50TmFtZScsIHNlbGVjdGVkQWdlbnROYW1lKTsKCiAgICAgICAgY29uc3QgbGVhZFR5cGVSYWRpbyA9IGRpYWxvZ0JveC5xdWVyeVNlbGVjdG9yKCdpbnB1dFtuYW1lPSJsZWFkVHlwZSJdOmNoZWNrZWQnKTsKICAgICAgICBnbG9iYWxMZWFkVHlwZSA9IGxlYWRUeXBlUmFkaW8gPyBsZWFkVHlwZVJhZGlvLnZhbHVlIDogZ2xvYmFsTGVhZFR5cGU7CgogICAgICAgIGlmICgoZGF5MSB8fCBkYXkyKSAmJiBzZWxlY3RlZERlYWxlcnNoaXApIHsKICAgICAgICAgICAgc2VsZWN0ZWREYXlzID0gZGF5MSAmJiBkYXkyID8gYCR7ZGF5MX0sICR7ZGF5Mn1gIDogZGF5MSB8fCBkYXkyOwogICAgICAgICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGRpYWxvZ0JveCk7CiAgICAgICAgICAgIHN0YXJ0VGltZXIoKTsKICAgICAgICAgICAgbGV0IGNsaWNrZWRMaW5rcyA9IDA7CiAgICAgICAgICAgIGNsaWNrTmV4dEJhdGNoKGxpbmtzLCAwLCBuYW1lQ291bnRzLCBjbGlja2VkTGlua3MpOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGFsZXJ0KCdZb3UgbXVzdCBzZWxlY3QgYXQgbGVhc3Qgb25lIGRheSBhbmQgZW50ZXIgYSBkZWFsZXJzaGlwIHRvIHN0YXJ0LicpOwogICAgICAgIH0KICAgIH0pOwogICAgfSkuY2F0Y2goZXJyb3IgPT4gY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBkYXRhOicsIGVycm9yKSk7Cn0KCiAgICBjb25zdCBvYnNlcnZlciA9IG5ldyBNdXRhdGlvbk9ic2VydmVyKChtdXRhdGlvbnMpID0+IHsKICAgICAgICBjb25zdCBsaW5rcyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJ2FbaWQqPSJDdXN0b21lck5hbWUiXScpOwogICAgICAgIGlmIChsaW5rcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIG1haW5Mb2dpYyhsaW5rcyk7CiAgICAgICAgICAgIG9ic2VydmVyLmRpc2Nvbm5lY3QoKTsKICAgICAgICB9CiAgICB9KTsKICAgIG9ic2VydmVyLm9ic2VydmUoZG9jdW1lbnQsIHsgY2hpbGRMaXN0OiB0cnVlLCBzdWJ0cmVlOiB0cnVlIH0pOwp9Cg=="}, {"name": "TrueBDC Click to Call", "options": {"check_for_updates": true, "user_modified": null, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434158, "data": {}}, "enabled": true, "position": 6, "file_url": "https://gist.github.com/geomodi/646cf91e71cadcbf6fd258f10d92d10c/raw/DPC2C2.0.user.js", "uuid": "2fcbca7f-5e74-41a4-ba18-bf17273036e2", "source": "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"}, {"name": "Tab to Popup Converter with Open RT, Advanced Remember Size, and <PERSON><PERSON><PERSON> Tooltip", "options": {"check_for_updates": true, "user_modified": 1717018624931, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434158, "data": {}}, "enabled": true, "position": 7, "file_url": "https://raw.githubusercontent.com/geomodi/tmpkscripts/main/Tab2PopuUp.user.js?token=GHSAT0AAAAAACKMSXMR7D66QQK2GVC2JAFYZMUNWPA", "uuid": "c3b5e8b5-e38c-450c-a075-80c207be03cb", "source": "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"}, {"name": "Auto Refresh with Configurable Timer, Pause, and Animation", "options": {"check_for_updates": true, "user_modified": 1711990236549, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434158, "data": {}}, "enabled": true, "position": 8, "uuid": "a346331c-1066-4806-ba53-f1d014ea50ff", "source": "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"}, {"name": "Check Internet Box Only", "options": {"check_for_updates": true, "user_modified": 1717095464326, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/reports/*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434159, "data": {}}, "enabled": true, "position": 9, "uuid": "4ac4a84b-78e1-4ecd-b04f-5d8bf3be3f6f", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBDaGVjayBJbnRlcm5ldCBCb3ggT25seQovLyBAdmVyc2lvbiAgICAgIDAuMQovLyBAZGVzY3JpcHRpb24gIFVuY2hlY2sgY2hlY2tib3hlcyBmb3IgIlZpc2l0cyIsICJTb2xkIiwgIkluIFNob3dyb29tIiwgIk92ZXIgNDUgTWluIiwgIkxlZnQgU2hvd3Jvb20iCi8vIEBhdXRob3IgICAgICAgQWxleAovLyBAbWF0Y2ggICAgICAgIGh0dHBzOi8vd3d3LmVsZWFkY3JtLmNvbS9ldm8yL2ZyZXNoL2VsZWFkLXY0NS9lbGVhZF90cmFjay9yZXBvcnRzLyoKLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIC8vIEVuc3VyZSB0aGUgIkludGVybmV0IiBjaGVja2JveCBpcyBjaGVja2VkCiAgICBjb25zdCBpbnRlcm5ldENoZWNrYm94ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ0ZpbHRlcnNfY2hrV2ViVXBzJyk7CiAgICBpZiAoaW50ZXJuZXRDaGVja2JveCkgewogICAgICAgIGludGVybmV0Q2hlY2tib3guY2hlY2tlZCA9IHRydWU7CiAgICB9CgogICAgLy8gTGlzdCBvZiBJRHMgZm9yIGFsbCBjaGVja2JveGVzIHRoYXQgbmVlZCB0byBiZSB1bmNoZWNrZWQKICAgIGNvbnN0IGNoZWNrYm94ZXNUb1VuY2hlY2sgPSBbCiAgICAgICAgJ0ZpbHRlcnNfY2hrQ2FtcGFpZ25VcHMnLAogICAgICAgICdGaWx0ZXJzX2Noa1NlcnZpY2VFcXVpdHknLAogICAgICAgICdGaWx0ZXJzX2Noa0FjcXVpc2l0aW9uTG90VXBzJywKICAgICAgICAnRmlsdGVyc19jaGtBY3F1aXNpdGlvbkFjcXVpcmVkJywKICAgICAgICAnRmlsdGVyc19jaGtBY3F1aXNpdGlvbkluU2hvd3Jvb20nLAogICAgICAgICdGaWx0ZXJzX2Noa0FjcXVpc2l0aW9uQWN0aXZlT3ZlcicsCiAgICAgICAgJ0ZpbHRlcnNfY2hrQWNxdWlzaXRpb25MZWZ0U2hvd3Jvb20nLAogICAgICAgICdGaWx0ZXJzX2Noa1NhbGVzTG90VXBzJywKICAgICAgICAnRmlsdGVyc19jaGtTYWxlc1NvbGQnLAogICAgICAgICdGaWx0ZXJzX2Noa1NhbGVzSW5TaG93cm9vbScsCiAgICAgICAgJ0ZpbHRlcnNfY2hrU2FsZXNBY3RpdmVPdmVyJywKICAgICAgICAnRmlsdGVyc19jaGtTYWxlc0xlZnRTaG93cm9vbScsCiAgICAgICAgLy8gQWRkIG1vcmUgY2hlY2tib3ggSURzIGFzIG5lY2Vzc2FyeQogICAgXTsKCiAgICAvLyBGdW5jdGlvbiB0byB1bmNoZWNrIGEgY2hlY2tib3ggYnkgaXRzIElECiAgICBmdW5jdGlvbiB1bmNoZWNrQ2hlY2tib3hCeUlkKGlkKSB7CiAgICAgICAgY29uc3QgY2hlY2tib3ggPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZChpZCk7CiAgICAgICAgaWYgKGNoZWNrYm94KSB7CiAgICAgICAgICAgIGNoZWNrYm94LmNoZWNrZWQgPSBmYWxzZTsKICAgICAgICB9CiAgICB9CgogICAgLy8gVW5jaGVjayBhbGwgc3BlY2lmaWVkIGNoZWNrYm94ZXMKICAgIGNoZWNrYm94ZXNUb1VuY2hlY2suZm9yRWFjaCh1bmNoZWNrQ2hlY2tib3hCeUlkKTsKfSkoKTs="}, {"name": "Add Calling Text and Shortcut to Change Time", "options": {"check_for_updates": true, "user_modified": 1717018651409, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/eleadToday/CompletePhoneContact.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434159, "data": {}}, "enabled": true, "position": 10, "uuid": "eeaf67a0-dbdd-41e2-a0f4-1f7773247b83", "source": "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"}, {"name": "Find Internet Up Activity", "options": {"check_for_updates": true, "user_modified": 1713535424299, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/NewProspects/history.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434159, "data": {}}, "enabled": true, "position": 11, "uuid": "fc55896e-6a5d-4e0c-bf38-310a21e4480e", "source": "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"}, {"name": "Activate Internet Sales Rep Tab", "options": {"check_for_updates": false, "user_modified": 1711738459727, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/index.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434159, "data": {}}, "enabled": true, "position": 12, "uuid": "0ba6d5e9-dc65-43ff-8aaa-7e28da8b0f8f", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBBY3RpdmF0ZSBJbnRlcm5ldCBTYWxlcyBSZXAgVGFiCi8vIEB2ZXJzaW9uICAgICAgMC4xCi8vIEBkZXNjcmlwdGlvbiAgQXV0b21hdGljYWxseSBhY3RpdmF0ZSB0aGUgSW50ZXJuZXQgU2FsZXMgUmVwIHRhYgovLyBAYXV0aG9yICAgICAgIEFsZXgKLy8gQG1hdGNoICAgICAgICBodHRwczovL3d3dy5lbGVhZGNybS5jb20vZXZvMi9mcmVzaC9lbGVhZC12NDUvZWxlYWRfdHJhY2svaW5kZXguYXNweCoKLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdsb2FkJywgZnVuY3Rpb24oKSB7CiAgICAgICAgc2V0VGltZW91dChmdW5jdGlvbigpIHsKICAgICAgICAgICAgLy8gR2V0IGFsbCBhbmNob3IgZWxlbWVudHMgd2l0aGluIHRoZSBkYXNoYm9hcmQKICAgICAgICAgICAgY29uc3QgYW5jaG9ycyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJ2EnKTsKCiAgICAgICAgICAgIC8vIEZpbmQgdGhlIGFuY2hvciB3aXRoIHRoZSB0ZXh0ICJJbnRlcm5ldCBTYWxlcyBSZXAiCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYW5jaG9ycy5sZW5ndGg7IGkrKykgewogICAgICAgICAgICAgICAgaWYgKGFuY2hvcnNbaV0udGV4dENvbnRlbnQudHJpbSgpID09PSAiSW50ZXJuZXQgU2FsZXMgUmVwIikgewogICAgICAgICAgICAgICAgICAgIC8vIFNpbXVsYXRlIGEgY2xpY2sgb24gdGhlIGZvdW5kIGFuY2hvcgogICAgICAgICAgICAgICAgICAgIGFuY2hvcnNbaV0uY2xpY2soKTsKICAgICAgICAgICAgICAgICAgICBicmVhazsKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgIH0sIDEwMDApOyAvLyBXYWl0IGZvciAxIHNlY29uZCBhZnRlciBwYWdlIGxvYWQgdG8gZW5zdXJlIGFsbCBlbGVtZW50cyBhcmUgbG9hZGVkCiAgICB9KTsKCn0pKCk7Cg=="}, {"name": "Dealership Web Search", "options": {"check_for_updates": true, "user_modified": null, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/NewProspects/history.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434160, "data": {}}, "enabled": true, "position": 13, "uuid": "cc6caac4-18da-4256-b625-308f334bdf8b", "source": "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"}, {"name": "Find Read Email and Internet Up Activities", "options": {"check_for_updates": true, "user_modified": 1717018697743, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/NewProspects/history.aspx*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434160, "data": {}}, "enabled": true, "position": 14, "uuid": "04821b89-5e0f-4949-ba84-3b9115917cfc", "source": "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"}, {"name": "New Email Notification", "options": {"check_for_updates": true, "user_modified": 1714674915209, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/elead_mail/Mailbox.aspx?Type=M"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434160, "data": {}}, "enabled": true, "position": 15, "uuid": "17f90aa8-cb1e-475d-afb5-928e6a8415f4", "source": "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"}, {"name": "Prevent Popup Closing", "options": {"check_for_updates": true, "user_modified": null, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/weblink/weblinkToday.aspx?bDashBoard=true&tab=liHotOpportunities"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434160, "data": {}}, "enabled": true, "position": 16, "uuid": "a749755e-f338-48cf-be5d-21cef6eb33e2", "source": "Ly8gPT1Vc2VyU2NyaXB0PT0KLy8gQG5hbWUgICAgICAgICBQcmV2ZW50IFBvcHVwIENsb3NpbmcKLy8gQG5hbWVzcGFjZSAgICBodHRwOi8vdGFtcGVybW9ua2V5Lm5ldC8KLy8gQHZlcnNpb24gICAgICAxLjAKLy8gQGRlc2NyaXB0aW9uICBQcmV2ZW50cyB0aGUgY2xvc2luZyBvZiBhIHNwZWNpZmljIHBvcHVwIHVubGVzcyBkb25lIG1hbnVhbGx5IGJ5IGFuIGFnZW50Ci8vIEBtYXRjaCAgICAgICAgaHR0cHM6Ly93d3cuZWxlYWRjcm0uY29tL2V2bzIvZnJlc2gvZWxlYWQtdjQ1L2VsZWFkX3RyYWNrL3dlYmxpbmsvd2VibGlua1RvZGF5LmFzcHg/YkRhc2hCb2FyZD10cnVlJnRhYj1saUhvdE9wcG9ydHVuaXRpZXMKLy8gQGdyYW50ICAgICAgICBub25lCi8vID09L1VzZXJTY3JpcHQ9PQoKKGZ1bmN0aW9uKCkgewogICAgJ3VzZSBzdHJpY3QnOwoKICAgIC8vIFN0b3JlIHRoZSBvcmlnaW5hbCB3aW5kb3cuY2xvc2UgbWV0aG9kCiAgICB2YXIgb3JpZ2luYWxDbG9zZSA9IHdpbmRvdy5jbG9zZTsKCiAgICAvLyBPdmVycmlkZSB0aGUgd2luZG93LmNsb3NlIG1ldGhvZAogICAgd2luZG93LmNsb3NlID0gZnVuY3Rpb24oKSB7CiAgICAgICAgLy8gQ2hlY2sgaWYgdGhlIGZ1bmN0aW9uIGlzIGNhbGxlZCBmcm9tIHRoZSBjb25zb2xlIG9yIGJ5IHVzZXIgaW50ZXJhY3Rpb24KICAgICAgICBpZiAoIShldmVudCAmJiBldmVudC5pc1RydXN0ZWQpKSB7CiAgICAgICAgICAgIC8vIElmIG5vdCBjYWxsZWQgbWFudWFsbHkgYnkgdGhlIHVzZXIsIHByZXZlbnQgdGhlIHBvcHVwIGZyb20gY2xvc2luZwogICAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIC8vIElmIGNhbGxlZCBtYW51YWxseSBieSB0aGUgdXNlciwgYWxsb3cgdGhlIHBvcHVwIHRvIGNsb3NlIHVzaW5nIHRoZSBvcmlnaW5hbCBtZXRob2QKICAgICAgICBvcmlnaW5hbENsb3NlLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7CiAgICB9Owp9KSgpOw=="}, {"name": "No Phone Lead Detector", "options": {"check_for_updates": true, "user_modified": 1717018899650, "comment": null, "compatopts_for_requires": true, "compat_wrappedjsobject": false, "compat_metadata": false, "compat_foreach": false, "compat_powerful_this": null, "sandbox": null, "noframes": null, "unwrap": null, "run_at": null, "tab_types": null, "override": {"use_includes": [], "orig_includes": [], "merge_includes": true, "use_matches": [], "orig_matches": ["https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/NewProspects/OpptyDetails.aspx*", "https://www.eleadcrm.com/evo2/fresh/elead-v45/elead_track/newprospects/add_update.asp*"], "merge_matches": true, "use_excludes": [], "orig_excludes": [], "merge_excludes": true, "use_connects": [], "orig_connects": [], "merge_connects": true, "use_blockers": [], "orig_run_at": "document-idle", "orig_noframes": null}}, "storage": {"ts": 1717095434160, "data": {}}, "enabled": true, "position": 17, "uuid": "a8034c19-dffc-43e9-9efc-80c72dd784ad", "source": "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"}]}